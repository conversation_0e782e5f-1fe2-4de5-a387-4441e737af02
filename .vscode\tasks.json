{"tasks": [{"type": "cppbuild", "label": "C/C++: clang.exe 生成活动文件", "command": "D:\\llvm-mingw-20250430-ucrt-x86_64\\bin\\clang.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}