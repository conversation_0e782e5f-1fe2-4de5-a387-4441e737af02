#include <easyx/easyx.h>
#include <conio.h>
#include <time.h>
#include <stdlib.h>
#include <cstdio>
#include <windows.h> // 添加此行以引入 VK_* 键值定义
#include <cmath> // 新增：引入数学库以使用 atan2 和 M_PI

#define WIDTH 800
#define HEIGHT 600
#define PLAYER_SPEED 5
#define ENEMY_SPEED 2 // 修改：将敌人的移动速度从 3 改为 2
#define BULLET_SPEED 20
// 新增：记录玩家的当前移动方向
int playerDirection = 0; // 默认向上
// 定义玩家结构体
#ifndef pieslice
void pieslice(int x, int y, int stangle, int endangle, int radius) {
	// 使用 arc 和 line 函数实现 pieslice 功能
	arc(x, y, stangle, endangle, radius, radius); // 修复 arc 函数调用时参数不足的问题
	line(x + radius * cos(stangle * M_PI / 180), y + radius * sin(stangle * M_PI / 180),
		x + radius * cos(endangle * M_PI / 180), y + radius * sin(endangle * M_PI / 180));
}
#endif
typedef struct {
    int x, y;
    int radius; // 新增：球形半径
    int health;
} Player;

// 定义敌人结构体
typedef struct {
    int x, y;
    int radius; // 新增：球形半径
    int health;
} Enemy;

// 定义子弹结构体（新增 direction 字段）
typedef struct {
    int x, y;
    int speed;
    bool active;
    int direction; // 新增字段：表示子弹的方向（0: 上，1: 下，2: 左，3: 右）
} Bullet;

// 定义敌人子弹结构体
typedef struct {
    int x, y;
    int speed;
    bool active;
    int direction; // 表示子弹的方向（0: 上，1: 下，2: 左，3: 右）
} EnemyBullet;

// 初始化玩家
void initPlayer(Player* player) {
    player->x = WIDTH / 2;
    player->y = HEIGHT - 100;
    player->radius = 15; // 半径为 15
    player->health = 100;
}

// 初始化敌人
void initEnemy(Enemy* enemy) {
    enemy->x = rand() % (WIDTH - 30);
    enemy->y = 50;
    enemy->radius = 15; // 半径为 15
    enemy->health = 100;
}

// 初始化子弹
void initBullet(Bullet* bullet) {
    bullet->x = 0;
    bullet->y = 0;
    bullet->speed = BULLET_SPEED; // 使用新的更快的子弹速度
    bullet->active = false;
    bullet->direction = 0; // 默认向上发射
}

// 初始化敌人子弹
void initEnemyBullet(EnemyBullet* bullet) {
    bullet->x = 0;
    bullet->y = 0;
    bullet->speed = BULLET_SPEED * 2; // 提升敌人子弹速度
    bullet->active = false;
    bullet->direction = 1; // 默认向下发射
}

// 绘制玩家
void drawPlayer(Player* player) {
    setfillcolor(LIGHTBLUE);
    solidcircle(player->x, player->y, player->radius); // 使用圆代替矩形
}

// 绘制敌人
void drawEnemy(Enemy* enemy) {
    setfillcolor(RED);
    solidcircle(enemy->x, enemy->y, enemy->radius); // 使用圆代替矩形
}

// 绘制子弹
void drawBullet(Bullet* bullet) {
    if (bullet->active) {
        setfillcolor(YELLOW);
        solidcircle(bullet->x, bullet->y, 5);
    }
}

// 绘制敌人子弹
void drawEnemyBullet(EnemyBullet* bullet) {
    if (bullet->active) {
        setfillcolor(RED);
        solidcircle(bullet->x, bullet->y, 5);
    }
}

// 移动玩家
void movePlayer(Player* player, int dx, int dy) {
    player->x += dx;
    player->y += dy;
    if (player->x < 0) player->x = 0;
    if (player->x + player->radius > WIDTH) player->x = WIDTH - player->radius;
    if (player->y < 0) player->y = 0;
    if (player->y + player->radius > HEIGHT) player->y = HEIGHT - player->radius;

    // 新增：根据移动方向更新玩家方向
    if (dy < 0) playerDirection = 0; // 向上
    else if (dy > 0) playerDirection = 1; // 向下
    else if (dx < 0) playerDirection = 2; // 向左
    else if (dx > 0) playerDirection = 3; // 向右
}

// 新增：定义敌人的移动方向
int enemyDirection = 1; // 默认向下

// 新增：定义扇形区域的参数
const int FOV_ANGLE = 60; // 视野角度（度）
const int FOV_DISTANCE = 150; // 视野距离

// 修改：检查玩家是否在扇形区域内（调整为后方视野）
bool isPlayerInFOV(Enemy* enemy, Player* player) {
    int dx = player->x - enemy->x;
    int dy = player->y - enemy->y;
    double angle = atan2(dy, dx) * 180 / M_PI;
    double enemyAngle = atan2(-1, 0) * 180 / M_PI; // 敌人默认向上

    // 根据敌人的移动方向调整角度范围（改为后方视野）
    double startAngle, endAngle;
    switch (enemyDirection) {
        case 0: // 向上（后方为下方）
            startAngle = 150;
            endAngle = 210;
            break;
        case 1: // 向下（后方为上方）
            startAngle = 30;
            endAngle = 330;
            break;
        case 2: // 向左（后方为右侧）
            startAngle = 60;
            endAngle = 120;
            break;
        case 3: // 向右（后方为左侧）
            startAngle = 240;
            endAngle = 300;
            break;
    }

    double angleDiff = fabs(angle - enemyAngle);
    if (angleDiff > 180) angleDiff = 360 - angleDiff;
    return angleDiff <= FOV_ANGLE / 2 && sqrt(dx * dx + dy * dy) <= FOV_DISTANCE;
}

// 新增：定义 RGB 宏
#ifndef RGB
#define RGB(r, g, b) ((unsigned long)((unsigned char)(r) | ((unsigned char)(g) << 8) | ((unsigned char)(b) << 16)))
#endif

// 新增：定义 pieslice 函数


// 修改：更新敌人移动方向和发射子弹的逻辑
void updateEnemyDirection(Enemy* enemy, Player* player) {
    int dx = player->x - enemy->x;
    int dy = player->y - enemy->y;
    double angle = atan2(dy, dx); // 使用 atan2 计算角度

    // 如果编译器不支持 M_PI，手动定义
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

    // 根据角度确定敌人移动方向
    if (angle >= -M_PI / 4 && angle < M_PI / 4) {
        enemyDirection = 3; // 向右
    } else if (angle >= M_PI / 4 && angle < 3 * M_PI / 4) {
        enemyDirection = 1; // 向下
    } else if (angle >= 3 * M_PI / 4 || angle < -3 * M_PI / 4) {
        enemyDirection = 2; // 向左
    } else {
        enemyDirection = 0; // 向上
    }

    // 新增：检查玩家是否在扇形区域内
    if (isPlayerInFOV(enemy, player)) {
        // 更新敌人移动方向
        enemy->x += cos(angle) * ENEMY_SPEED;
        enemy->y += sin(angle) * ENEMY_SPEED;

        // 新增：敌人发射子弹逻辑
        static clock_t lastEnemyShotTime = 0;
        const int ENEMY_SHOT_INTERVAL = 2000; // 敌人发射间隔时间为2000毫秒

        clock_t currentEnemyShotTime = clock();
        if ((currentEnemyShotTime - lastEnemyShotTime) * 1000 / CLOCKS_PER_SEC >= ENEMY_SHOT_INTERVAL) {
            lastEnemyShotTime = currentEnemyShotTime;

            static EnemyBullet enemyBullet;
            initEnemyBullet(&enemyBullet);

            enemyBullet.x = enemy->x + enemy->radius / 2; // 调整为敌人前方发射
            enemyBullet.y = enemy->y + enemy->radius;    // 调整为敌人下方发射
            enemyBullet.active = true;
            enemyBullet.direction = enemyDirection; // 设置子弹方向与敌人移动方向一致
        }
    } else {
        // 如果玩家不在扇形区域内，敌人保持原方向移动
        switch (enemyDirection) {
            case 0: // 向上
                enemy->y -= ENEMY_SPEED;
                break;
            case 1: // 向下
                enemy->y += ENEMY_SPEED;
                break;
            case 2: // 向左
                enemy->x -= ENEMY_SPEED;
                break;
            case 3: // 向右
                enemy->x += ENEMY_SPEED;
                break;
        }
    }
}

// 移动敌人
void moveEnemy(Enemy* enemy, Player* player) {
    updateEnemyDirection(enemy, player); // 更新敌人移动方向

    // 根据方向移动敌人
    switch (enemyDirection) {
        case 0: // 向上
            enemy->y -= ENEMY_SPEED;
            break;
        case 1: // 向下
            enemy->y += ENEMY_SPEED;
            break;
        case 2: // 向左
            enemy->x -= ENEMY_SPEED;
            break;
        case 3: // 向右
            enemy->x += ENEMY_SPEED;
            break;
    }

    // 如果敌人超出屏幕范围，重新生成位置
    if (enemy->x < 0 || enemy->x + enemy->radius > WIDTH || enemy->y < 0 || enemy->y + enemy->radius > HEIGHT) {
        enemy->x = rand() % (WIDTH - 30);
        enemy->y = 50;
    }
}

// 移动子弹（根据方向调整移动逻辑）
void moveBullet(Bullet* bullet) {
    if (bullet->active) {
        switch (bullet->direction) {
            case 0: // 向上
                bullet->y -= bullet->speed;
                break;
            case 1: // 向下
                bullet->y += bullet->speed;
                break;
            case 2: // 向左
                bullet->x -= bullet->speed;
                break;
            case 3: // 向右
                bullet->x += bullet->speed;
                break;
        }
        // 如果子弹超出屏幕范围，标记为非活动状态
        if (bullet->x < 0 || bullet->x > WIDTH || bullet->y < 0 || bullet->y > HEIGHT) {
            bullet->active = false;
        }
    }
}

// 移动敌人子弹
void moveEnemyBullet(EnemyBullet* bullet) {
    if (bullet->active) {
        switch (bullet->direction) {
            case 0: // 向上
                bullet->y -= bullet->speed;
                break;
            case 1: // 向下
                bullet->y += bullet->speed;
                break;
            case 2: // 向左
                bullet->x -= bullet->speed;
                break;
            case 3: // 向右
                bullet->x += bullet->speed;
                break;
        }
        // 如果子弹超出屏幕范围，标记为非活动状态
        if (bullet->x < 0 || bullet->x > WIDTH || bullet->y < 0 || bullet->y > HEIGHT) {
            bullet->active = false;
        }
    }
}

// 检测碰撞（改为圆形碰撞检测）
bool checkCollision(int x1, int y1, int r1, int x2, int y2, int r2) {
    int dx = x1 - x2;
    int dy = y1 - y2;
    int distanceSquared = dx * dx + dy * dy;
    int radiusSum = r1 + r2;
    return distanceSquared <= radiusSum * radiusSum;
}

// 手动定义缺失的虚拟键码（如果编译器未正确引入）
#ifndef VK_A
#define VK_A 0x41
#endif
#ifndef VK_D
#define VK_D 0x44
#endif
#ifndef VK_W
#define VK_W 0x57
#endif
#ifndef VK_S
#define VK_S 0x53
#endif

// 新增：绘制三角形视野（调整为后方视野）
void drawFOV(Enemy* enemy) {
    setlinecolor(GREEN);
    setfillcolor(RGB(0, 255, 0));

    // 定义三角形的顶点
    int apexX = enemy->x;
    int apexY = enemy->y;
    int baseLength = 50; // 三角形底边长度
    int height = 100; // 三角形高度

    // 根据敌人的移动方向计算三角形顶点（改为后方视野）
    int leftX, leftY, rightX, rightY;
    switch (enemyDirection) {
        case 0: // 向上（后方为下方）
            leftX = apexX - baseLength / 2;
            leftY = apexY - height;
            rightX = apexX + baseLength / 2;
            rightY = apexY - height;
            break;
        case 1: // 向下（后方为上方）
            leftX = apexX - baseLength / 2;
            leftY = apexY + height;
            rightX = apexX + baseLength / 2;
            rightY = apexY + height;
            break;
        case 2: // 向左（后方为右侧）
            leftX = apexX - height;
            leftY = apexY - baseLength / 2;
            rightX = apexX - height;
            rightY = apexY + baseLength / 2;
            break;
        case 3: // 向右（后方为左侧）
            leftX = apexX + height;
            leftY = apexY - baseLength / 2;
            rightX = apexX + height;
            rightY = apexY + baseLength / 2;
            break;
    }

    // 绘制三角形
    line(apexX, apexY, leftX, leftY);
    line(apexX, apexY, rightX, rightY);
    line(leftX, leftY, rightX, rightY);
}

// 主函数
int main() {
    // 初始化图形窗口
    initgraph(WIDTH, HEIGHT);
    srand((unsigned)time(NULL));

    // 设置图形窗口为焦点窗口
    SetFocus(GetHWnd()); // 确保图形窗口获得焦点，直接接收键盘信号

    // 初始化玩家、敌人和子弹
    Player player;
    Enemy enemy;
    Bullet bullet;
    initPlayer(&player);
    initEnemy(&enemy);
    initBullet(&bullet);

    // 初始化敌人子弹
    EnemyBullet enemyBullet;
    initEnemyBullet(&enemyBullet);

    // 创建双缓冲
    IMAGE buffer;
    getimage(&buffer, 0, 0, WIDTH, HEIGHT);

    // 记录上一帧的时间戳
    clock_t lastTime = clock();
    const int FPS = 60; // 目标帧率
    const int frameDelay = 1000 / FPS; // 每帧延迟时间（毫秒）

    // 新增：记录上一次发射子弹的时间戳
    clock_t lastShotTime = 0;
    const int SHOT_INTERVAL = 500; // 发射间隔时间为500毫秒

    // 主循环
    while (true) {
        // 计算当前帧的时间戳
        clock_t currentTime = clock();
        int elapsedTime = (currentTime - lastTime) * 1000 / CLOCKS_PER_SEC;

        // 如果时间不足一帧，则休眠
        if (elapsedTime < frameDelay) {
            Sleep(frameDelay - elapsedTime);
            continue;
        }

        // 更新时间戳
        lastTime = currentTime;

        // 清屏（双缓冲）
        cleardevice();

        // 设置背景颜色为淡绿色
        setbkcolor(RGB(204, 255, 204)); // 淡绿色 RGB 值
        cleardevice(); // 再次清屏以应用背景颜色

        // 使用 GetAsyncKeyState() 检测键盘状态
        if (GetAsyncKeyState(VK_A)) {
            movePlayer(&player, -PLAYER_SPEED, 0);
        }
        if (GetAsyncKeyState(VK_D)) {
            movePlayer(&player, PLAYER_SPEED, 0);
        }
        if (GetAsyncKeyState(VK_W)) {
            movePlayer(&player, 0, -PLAYER_SPEED);
        }
        if (GetAsyncKeyState(VK_S)) {
            movePlayer(&player, 0, PLAYER_SPEED);
        }

        // 新增：检测空格键并控制发射频率
        if (GetAsyncKeyState(VK_SPACE)) {
            clock_t currentShotTime = clock();
            if ((currentShotTime - lastShotTime) * 1000 / CLOCKS_PER_SEC >= SHOT_INTERVAL) {
                lastShotTime = currentShotTime;
                bullet.x = player.x + player.radius / 2;
                bullet.y = player.y + player.radius / 2;
                bullet.active = true;
                bullet.direction = playerDirection; // 动态设置子弹方向
                bullet.speed = BULLET_SPEED * 1.5; // 动态调整子弹速度
            }
        }

        // 移动敌人和子弹
        moveEnemy(&enemy, &player); // 传入玩家位置参数
        moveBullet(&bullet);
        moveEnemyBullet(&enemyBullet);

        // 检测碰撞（改为圆形碰撞检测）
        if (checkCollision(bullet.x, bullet.y, 5, enemy.x, enemy.y, enemy.radius)) {
            bullet.active = false;
            enemy.health -= 10;
            if (enemy.health <= 0) {
                initEnemy(&enemy);
                enemy.health = 100;
            }
        }

        // 新增：检测敌人子弹与玩家的碰撞
        if (checkCollision(enemyBullet.x, enemyBullet.y, 5, player.x, player.y, player.radius)) {
            enemyBullet.active = false;
            player.health -= 10;
            if (player.health <= 0) {
                cleardevice();
                settextcolor(RED);
                outtextxy(WIDTH / 2 - 50, HEIGHT / 2, "Game Over!");
                Sleep(2000);
                break;
            }
        }

        // 新增：检测玩家子弹与敌人的碰撞
        if (checkCollision(bullet.x, bullet.y, 5, enemy.x, enemy.y, enemy.radius)) {
            bullet.active = false;
            enemy.health -= 10;
            if (enemy.health <= 0) {
                initEnemy(&enemy);
                enemy.health = 100;
            }
        }

        // 新增：检测敌人子弹与敌人的碰撞
        if (checkCollision(enemyBullet.x, enemyBullet.y, 5, enemy.x, enemy.y, enemy.radius)) {
            enemyBullet.active = false;
            enemy.health -= 10;
            if (enemy.health <= 0) {
                initEnemy(&enemy);
                enemy.health = 100;
            }
        }

        // 绘制玩家、敌人、子弹和敌人子弹
        drawPlayer(&player);
        drawEnemy(&enemy);
        drawBullet(&bullet);
        drawEnemyBullet(&enemyBullet);

        // 新增：绘制扇形区域
        drawFOV(&enemy);

        // 显示生命值
        settextcolor(WHITE);
        outtextxy(10, 10, "Player Health:");
        char healthStr[10];
        sprintf(healthStr, "%d", player.health);
        outtextxy(100, 10, healthStr);

        // 检测游戏结束（改为圆形碰撞检测）
        if (checkCollision(player.x, player.y, player.radius, enemy.x, enemy.y, enemy.radius)) {
            player.health -= 10;
            if (player.health <= 0) {
                cleardevice();
                settextcolor(RED);
                outtextxy(WIDTH / 2 - 50, HEIGHT / 2, "Game Over!");
                Sleep(2000);
                break;
            }
        }

        // 将当前绘制内容存储到缓冲区
        getimage(&buffer, 0, 0, WIDTH, HEIGHT);

        // 双缓冲刷新
        putimage(0, 0, &buffer);

        // 确保帧率稳定
        elapsedTime = (clock() - currentTime) * 1000 / CLOCKS_PER_SEC;
        if (elapsedTime < frameDelay) {
            Sleep(frameDelay - elapsedTime);
        }
    }

    // 关闭图形窗口
    closegraph();
    return 0;
}
