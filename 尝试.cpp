#include <windows.h>
#include <gdiplus.h>
#include <stdio.h>
#include <iostream>

#pragma comment(lib, "gdiplus.lib")

using namespace Gdiplus;

// 窗口过程函数的前向声明
LRESULT CALLBACK WndProc(HWND, UINT, WPARAM, LPARAM);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // 初始化GDI+
    GdiplusStartupInput gdiplusStartupInput;
    ULONG_PTR gdiplusToken;
    GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);

    // 注册窗口类
    WNDCLASSEX wcex;
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = WndProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = 0;
    wcex.hInstance = hInstance;
    wcex.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wcex.hCursor = LoadCursor(NULL, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = NULL;
    wcex.lpszClassName = L"BackgroundDemo";
    wcex.hIconSm = LoadIcon(NULL, IDI_APPLICATION);

    if (!RegisterClassEx(&wcex)) {
        MessageBox(NULL, L"窗口注册失败！", L"错误", MB_ICONERROR);
        return 1;
    }

    // 创建窗口
    HWND hWnd = CreateWindow(
        L"BackgroundDemo", L"整张图片作为背景演示",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        NULL, NULL, hInstance, NULL);

    if (!hWnd) {
        MessageBox(NULL, L"窗口创建失败！", L"错误", MB_ICONERROR);
        return 1;
    }

    // 显示窗口
    ShowWindow(hWnd, nCmdShow);
    UpdateWindow(hWnd);

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 关闭GDI+
    GdiplusShutdown(gdiplusToken);

    return (int)msg.wParam;
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    static Image* pImage = NULL;

    switch (message) {
    case WM_CREATE:
        // 尝试加载图像
        pImage = new Image(L"潜伏/rock.png");
        if (pImage->GetLastStatus() != Ok) {
            MessageBox(hWnd, L"无法加载图像文件 'rock.png'！", L"错误", MB_ICONERROR);
            delete pImage;
            pImage = NULL;
        }
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);

            if (pImage) {
                Graphics graphics(hdc);

                // 获取窗口客户区大小
                RECT rect;
                GetClientRect(hWnd, &rect);
                int width = rect.right - rect.left;
                int height = rect.bottom - rect.top;

                // 直接将整张图片缩放到窗口大小
                graphics.DrawImage(pImage, 0, 0, width, height);

                // 绘制文本
                SolidBrush brush(Color(255, 255, 255, 255));
                FontFamily fontFamily(L"Arial");
                Font font(&fontFamily, 12, FontStyleRegular, UnitPoint);
                PointF pointF(10.0f, 10.0f);
                graphics.DrawString(L"整张图片缩放作为背景 (按ESC键退出)", -1, &font, pointF, &brush);
            }

            EndPaint(hWnd, &ps);
        }
        break;

    case WM_KEYDOWN:
        if (wParam == VK_ESCAPE) {
            DestroyWindow(hWnd);
        }
        break;

    case WM_DESTROY:
        if (pImage) {
            delete pImage;
            pImage = NULL;
        }
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}
