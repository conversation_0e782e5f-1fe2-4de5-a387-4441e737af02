#include <easyx/easyx.h>
#include <conio.h>
#include <string.h>
#include <time.h>
#include<stdio.h>
#include<math.h>
#include<windows.h> // 添加Windows.h头文件
#include <algorithm> // 为了使用std::max
//存在问题战争迷雾失效 ，玩家可以相互治疗 玩家二的炸弹功能出现问题 玩家一子弹向右发射时存在子弹不显示的bug
// 游戏常量定义
// 使用系统屏幕分辨率
int WINDOW_WIDTH;
int WINDOW_HEIGHT;
#define BLOCK_SIZE 40
#define ENEMY_SPEED 3.5  // 原来是2，现在增加到3.5
#define SOLDIER_SPEED 0.5  // 步幅更小
#define BULLET_SPEED 14    // 原来是7，现在翻倍为14
#define MAX_HEALTH 3      // 玩家最大生命值

#define SAFE_ZONE_RADIUS 2
#define MAP_WIDTH 8000    // 从4000增加到8000
#define MAP_HEIGHT 8000   // 从4000增加到8000
// 颜色定义
#define GRAY RGB(128, 128, 128)
// 添加地图和迷雾系统相关定义
#define MAP_GRID_SIZE BLOCK_SIZE
#define MAP_GRID_WIDTH (MAP_WIDTH / MAP_GRID_SIZE)
#define MAP_GRID_HEIGHT (MAP_HEIGHT / MAP_GRID_SIZE)

// 地图格子类型
#define GRID_EMPTY 0
#define GRID_OBSTACLE 1
#define GRID_ENEMY 2


// 迷雾状态
#define FOG_UNKNOWN 0  // 未探索
#define FOG_EXPLORED 1 // 已探索

// 添加地图和迷雾数组
int map_grid[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 存储地图信息
int fog_of_war[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 存储迷雾状态

// 结构体定义
typedef struct {
	int x, y, site;
	int health;       // 玩家生命值
	DWORD invincible;   // 无敌时间（毫秒）
	DWORD last_hit_time; // 上次受伤时间
	bool is_dead;     // 是否死亡
	bool being_rescued; // 是否正在被救助
	DWORD death_time; // 死亡时间
	bool being_healed; // 是否正在被治疗
	DWORD heal_start_time; // 开始治疗的时间
	float heal_progress; // 治疗进度（0.0-1.0）
} Soldier;

// 敌人类型枚举
typedef enum {
	ENEMY_NORMAL = 0,  // 普通敌人
	ENEMY_SNIPER = 1,  // 狙击手：子弹伤害更高，射程更远射速更快，但发射间隔更长
	ENEMY_TANK = 2     // 坦克：拥有三滴血
} EnemyType;

typedef struct Enemy {
	int x, y;
	int target_player; // 目标玩家：0=玩家1，1=玩家2，-1=未设置
	int frame_count;   // 帧计数器，用于控制目标切换频率
	DWORD last_attack_time; // 上次攻击时间，用于限制攻击频率
	EnemyType type;    // 敌人类型
	int health;        // 敌人生命值（普通敌人和狙击手为1，坦克为3）
	struct Enemy* next;
} Enemy;

// 障碍物类型枚举
typedef enum {
	OBSTACLE_ROCK = 0,  // 岩石障碍物（可被炸弹炸毁）
	OBSTACLE_WATER = 1  // 水域障碍物（不可被炸弹炸毁，子弹可穿过）
} ObstacleType;

typedef struct Obstacle {
	int x, y;
	ObstacleType type;  // 障碍物类型
	struct Obstacle* next;
} Obstacle;


typedef struct Bullet {
	int x, y;
	int dir_x, dir_y;
	int active;
	struct Bullet* next;
} Bullet;

// 敌人子弹结构体
typedef struct EnemyBullet {
	int x, y;
	int dir_x, dir_y;
	int active;
	DWORD create_time;  // 创建时间
	EnemyType type;     // 子弹来源类型（哪种敌人发射的）
	struct EnemyBullet* next;
} EnemyBullet;
// 轰炸区域中的单个格子
typedef struct BombingCell {
	int x, y;          // 格子坐标
	struct BombingCell* next;
} BombingCell;

typedef struct BombingArea {
	BombingCell* cells;    // 轰炸区域中的格子链表
	int cell_count;        // 格子数量（最多16个）
	int active;            // 是否激活（1=激活，0=未激活）
	int marked;            // 是否已标记（1=已标记，0=未标记）
	DWORD create_time;     // 创建时间
	DWORD mark_time;       // 标记时间（白色显示后的时间）
	struct BombingArea* next;
} BombingArea;

// 玩家2的炸弹结构体
typedef struct Bomb {
	int x, y;          // 炸弹的左上角坐标
	DWORD create_time; // 创建时间
	int active;        // 是否激活（1=激活，0=已爆炸）
	struct Bomb* next;
} Bomb;
// 添加摄像机相关变量
typedef struct {
	int x, y;  // 摄像机左上角坐标
} Camera;

// 全局变量
Soldier soldier1 = {WINDOW_WIDTH / 4 - BLOCK_SIZE / 2, WINDOW_HEIGHT - BLOCK_SIZE * 2, 2, 3, 0, 0}; // 玩家1，初始3条命
Soldier soldier2 = {WINDOW_WIDTH * 3 / 4 - BLOCK_SIZE / 2, WINDOW_HEIGHT - BLOCK_SIZE * 2, 4, 3, 0, 0}; // 玩家2，初始3条命
Enemy* enemy_head = NULL;
Obstacle* obstacle_head = NULL;

Bullet* bullet_head1 = NULL; // 玩家1的子弹
Bullet* bullet_head2 = NULL; // 玩家2的子弹（不再使用，保留以兼容现有代码）
EnemyBullet* enemy_bullet_head = NULL; // 敌人子弹链表头
BombingArea* bombing_area_head = NULL; // 添加轰炸区域链表头
Bomb* bomb_head = NULL; // 玩家2的炸弹链表头
int game_over = 0;
int level = 1;
int player1_score = 0;  // 玩家1得分
int player2_score = 0;  // 玩家2得分
int player1_kills = 0;  // 玩家1击杀数量（用于子弹升级）
int player2_kills = 0;  // 玩家2击杀数量（用于炸弹升级）
int enemy_count = 0;    // 当前敌人数量

// 玩家升级系统
int player1_level = 1;  // 玩家1当前等级
int player2_level = 1;  // 玩家2当前等级

// 升级配置 - 每个等级所需的击杀数
const int UPGRADE_KILLS[] = {0, 5, 15, 30, 50, 75, 105, 140, 180, 225, 275}; // 0级到10级所需击杀数
const int MAX_LEVEL = 10; // 最大等级

// 玩家1升级加成（子弹相关）
typedef struct {
    float bullet_speed_multiplier;  // 子弹速度倍数
    float bullet_damage_multiplier; // 子弹伤害倍数
    int bullet_penetration;         // 子弹穿透数量
    float attack_speed_multiplier;  // 攻击速度倍数
    int health_bonus;               // 额外生命值
    float movement_speed_multiplier; // 移动速度倍数
} Player1Upgrade;

// 玩家2升级加成（炸弹相关）
typedef struct {
    int explosion_size;             // 爆炸范围大小
    float explosion_damage_multiplier; // 爆炸伤害倍数
    float bomb_cooldown_multiplier; // 炸弹冷却时间倍数
    int max_bombs;                  // 最大同时存在的炸弹数量
    int health_bonus;               // 额外生命值
    float movement_speed_multiplier; // 移动速度倍数
} Player2Upgrade;

// 升级配置表
const Player1Upgrade PLAYER1_UPGRADES[MAX_LEVEL + 1] = {
    {1.0f, 1.0f, 0, 1.0f, 0, 1.0f},  // 等级0：占位符（不使用）
    {1.0f, 1.0f, 0, 1.0f, 0, 1.0f},  // 等级1：基础属性
    {1.2f, 1.0f, 0, 1.1f, 0, 1.0f},  // 等级2：子弹速度+20%，攻速+10%
    {1.5f, 1.2f, 0, 1.2f, 1, 1.1f},  // 等级3：子弹速度+50%，伤害+20%，攻速+20%，生命+1，移速+10%
    {1.8f, 1.5f, 1, 1.3f, 1, 1.1f},  // 等级4：子弹速度+80%，伤害+50%，穿透1个，攻速+30%，生命+1，移速+10%
    {2.0f, 1.8f, 1, 1.4f, 2, 1.2f},  // 等级5：子弹速度+100%，伤害+80%，穿透1个，攻速+40%，生命+2，移速+20%
    {2.3f, 2.0f, 2, 1.5f, 2, 1.2f},  // 等级6：子弹速度+130%，伤害+100%，穿透2个，攻速+50%，生命+2，移速+20%
    {2.6f, 2.3f, 2, 1.6f, 3, 1.3f},  // 等级7：子弹速度+160%，伤害+130%，穿透2个，攻速+60%，生命+3，移速+30%
    {3.0f, 2.6f, 3, 1.8f, 3, 1.3f},  // 等级8：子弹速度+200%，伤害+160%，穿透3个，攻速+80%，生命+3，移速+30%
    {3.4f, 3.0f, 3, 2.0f, 4, 1.4f},  // 等级9：子弹速度+240%，伤害+200%，穿透3个，攻速+100%，生命+4，移速+40%
    {3.8f, 3.5f, 4, 2.2f, 5, 1.5f}   // 等级10：子弹速度+280%，伤害+250%，穿透4个，攻速+120%，生命+5，移速+50%
};

const Player2Upgrade PLAYER2_UPGRADES[MAX_LEVEL + 1] = {
    {3, 1.0f, 1.0f, 1, 0, 1.0f},     // 等级0：占位符（不使用）
    {3, 1.0f, 1.0f, 1, 0, 1.0f},     // 等级1：3x3爆炸，基础伤害，基础冷却，1个炸弹
    {3, 1.2f, 0.9f, 1, 0, 1.0f},     // 等级2：3x3爆炸，伤害+20%，冷却-10%
    {4, 1.5f, 0.8f, 1, 1, 1.1f},     // 等级3：4x4爆炸，伤害+50%，冷却-20%，生命+1，移速+10%
    {4, 1.8f, 0.7f, 2, 1, 1.1f},     // 等级4：4x4爆炸，伤害+80%，冷却-30%，2个炸弹，生命+1，移速+10%
    {5, 2.0f, 0.6f, 2, 2, 1.2f},     // 等级5：5x5爆炸，伤害+100%，冷却-40%，2个炸弹，生命+2，移速+20%
    {5, 2.3f, 0.5f, 2, 2, 1.2f},     // 等级6：5x5爆炸，伤害+130%，冷却-50%，2个炸弹，生命+2，移速+20%
    {6, 2.6f, 0.4f, 3, 3, 1.3f},     // 等级7：6x6爆炸，伤害+160%，冷却-60%，3个炸弹，生命+3，移速+30%
    {6, 3.0f, 0.35f, 3, 3, 1.3f},    // 等级8：6x6爆炸，伤害+200%，冷却-65%，3个炸弹，生命+3，移速+30%
    {7, 3.5f, 0.3f, 3, 4, 1.4f},     // 等级9：7x7爆炸，伤害+250%，冷却-70%，3个炸弹，生命+4，移速+40%
    {7, 4.0f, 0.25f, 4, 5, 1.5f}     // 等级10：7x7爆炸，伤害+300%，冷却-75%，4个炸弹，生命+5，移速+50%
};

// 狂暴模式相关变量
bool rage_mode_active = false;           // 狂暴模式是否激活
DWORD rage_mode_start_time = 0;          // 狂暴模式开始时间
DWORD last_rage_mode_time = 0;           // 上次狂暴模式触发时间
int rage_mode_count = 0;                 // 狂暴模式触发次数
const int RAGE_MODE_INTERVAL = 60000;    // 狂暴模式触发间隔（60秒）
const int RAGE_MODE_DURATION = 15000;    // 狂暴模式持续时间（15秒）
const int NORMAL_ENEMY_SPAWN_RATE = 1;   // 正常模式下的敌人生成速率
int current_enemy_spawn_rate = NORMAL_ENEMY_SPAWN_RATE; // 当前敌人生成速率

// 攻击频率限制相关变量
DWORD last_player1_attack_time = 0;  // 玩家1上次攻击时间
DWORD last_player2_attack_time = 0;  // 玩家2上次攻击时间
const int PLAYER_ATTACK_COOLDOWN = 250;  // 玩家攻击冷却时间（毫秒）：1000ms/4=250ms
const int ENEMY_ATTACK_COOLDOWN = 500;   // 敌人攻击冷却时间（毫秒）：1000ms/2=500ms

// 贴图相关变量
IMAGE ground_texture;  // 地面贴图
IMAGE rock_texture;    // 岩石障碍物贴图
IMAGE water_texture;   // 水域障碍物贴图
IMAGE warning_texture; // 警告贴图（黄色）
IMAGE red_warning_texture; // 红色警告贴图（即将爆炸）
const int MAX_ENEMIES = 50; // 最大敌人数量（从30增加到50）
int obstacle_count = 0; // 当前障碍物数量
const int MAX_OBSTACLES = 500; // 最大障碍物数量（从300增加到500）
DWORD last_obstacle_spawn_time = 0; // 上次生成障碍物的时间

int soldier1_direction = 2;   // 玩家1初始朝下（0:上, 1:右, 2:下, 3:左）
int soldier2_direction = 2;   // 玩家2初始朝下（0:上, 1:右, 2:下, 3:左）
DWORD last_bombing_check_time = 0;    // 上次轰炸检查的时间
Camera camera1 = {0, 0};  // 玩家1的摄像机位置
Camera camera2 = {0, 0};  // 玩家2的摄像机位置
// 存档结构体定义
typedef struct {
    // 玩家状态
    int soldier1_x, soldier1_y, soldier1_site, soldier1_health;
    int soldier2_x, soldier2_y, soldier2_site, soldier2_health;
    bool soldier1_is_dead, soldier2_is_dead;
    DWORD soldier1_invincible, soldier2_invincible;
    DWORD soldier1_last_hit_time, soldier2_last_hit_time;
    DWORD soldier1_death_time, soldier2_death_time;
    bool soldier1_being_rescued, soldier2_being_rescued;
    bool soldier1_being_healed, soldier2_being_healed;
    DWORD soldier1_heal_start_time, soldier2_heal_start_time;
    float soldier1_heal_progress, soldier2_heal_progress;

    // 游戏状态
    int player1_score, player2_score;
    int player1_kills, player2_kills;
    int player1_level, player2_level; // 玩家等级
    int level;
    int enemy_count;
    int obstacle_count;

    // 狂暴模式状态
    bool rage_mode_active;
    int rage_mode_count;
    DWORD rage_mode_start_time;
    DWORD last_rage_mode_time;
    int current_enemy_spawn_rate;

    // 存档时间戳
    DWORD save_time;
} GameSave;

// 函数声明
void init_game();
void draw_game();
void spawn_enemy();
void spawn_obstacle(int count);
void update_enemies();
void check_collision();
void handle_input();
void game_over_screen();

int obstacle_collision(int x, int y);
bool is_in_purple_area(int x, int y);
void fire_bullet1(int site);  // 玩家1发射子弹
void place_bomb();           // 玩家2放置炸弹
void update_bullets1();       // 更新玩家1子弹
void update_bombs();          // 更新玩家2炸弹
void draw_bullets1();         // 绘制玩家1子弹
void draw_bombs();            // 绘制玩家2炸弹
void enemy_fire_bullet(Enemy* enemy);
void update_enemy_bullets();
void draw_enemy_bullets();
void update_camera1();        // 更新玩家1摄像机
void update_camera2();        // 更新玩家2摄像机
int world_to_screen_x1(int world_x);  // 玩家1世界坐标转屏幕坐标
int world_to_screen_y1(int world_y);
int world_to_screen_x2(int world_x);  // 玩家2世界坐标转屏幕坐标
int world_to_screen_y2(int world_y);
void draw_minimap1();         // 绘制玩家1小地图
void draw_minimap2();         // 绘制玩家2小地图

void game_loop();
void free_bombing_areas();
void check_bombing_areas();
void spawn_random_bombing_areas(int count);
void spawn_random_bombing_area();
void check_bombing_area();
void spawn_offscreen_obstacles(int count);
bool is_in_player_view(int x, int y);

void update_camera();
int world_to_screen_x(int world_x);
int world_to_screen_y(int world_y);
int screen_to_world_x(int screen_x);
int screen_to_world_y(int screen_y);
void init_map();
void draw_minimap();
void show_story();
void check_rage_mode(); // 检查和更新狂暴模式状态
void start_rage_mode(); // 开始狂暴模式
void end_rage_mode();   // 结束狂暴模式
float get_rage_mode_multiplier(); // 获取当前狂暴模式的增强系数
void draw_rage_mode_progress_bar(); // 绘制狂暴模式进度条

// 存档相关函数
void save_game(); // 保存游戏状态
bool load_game(); // 加载游戏状态
void show_start_menu(); // 显示开始菜单
void draw_button(int x, int y, int width, int height, const char* text, bool highlighted); // 绘制按钮

// 升级系统相关函数
void check_player_upgrades(); // 检查玩家升级
int get_player_level(int kills); // 根据击杀数获取等级
void draw_minimap1_level_info(int minimap_x, int minimap_y, int minimap_size); // 绘制玩家1小地图等级信息
void draw_minimap2_level_info(int minimap_x, int minimap_y, int minimap_size); // 绘制玩家2小地图等级信息
Player1Upgrade get_player1_upgrade(); // 获取玩家1当前升级加成
Player2Upgrade get_player2_upgrade(); // 获取玩家2当前升级加成

// 安全内存管理函数
int get_safe_upgrade_kills(int level); // 安全的数组访问函数
float calculate_safe_progress(int current_kills, int level); // 安全的进度计算函数
void safe_free_enemies(); // 安全释放敌人内存
void safe_free_bullets(); // 安全释放子弹内存
void safe_free_bombing_areas(); // 安全释放轰炸区域内存
void safe_free_bombs(); // 安全释放炸弹内存
void cleanup_all_memory(); // 清理所有内存

int count_obstacles(); // 新增函数声明
bool is_in_player_view(int x, int y); // 函数声明
// 初始化游戏
void init_game() {
	// 获取系统屏幕分辨率
	WINDOW_WIDTH = GetSystemMetrics(SM_CXSCREEN);
	WINDOW_HEIGHT = GetSystemMetrics(SM_CYSCREEN);

	// 初始化图形窗口（全屏模式）
	initgraph(WINDOW_WIDTH, WINDOW_HEIGHT, EX_SHOWCONSOLE);

	// 设置窗口为全屏模式
	HWND hwnd = GetHWnd();
	SetWindowLong(hwnd, GWL_STYLE, GetWindowLong(hwnd, GWL_STYLE) & ~WS_CAPTION);
	SetWindowPos(hwnd, HWND_TOP, 0, 0, WINDOW_WIDTH, WINDOW_HEIGHT, SWP_SHOWWINDOW);

	setbkcolor(BLACK);
	cleardevice();
	settextcolor(WHITE);
	settextstyle(20, 0, _T("宋体"));
	srand((unsigned)time(NULL));

	// 启用双缓冲绘图
	BeginBatchDraw();

	// 将两个玩家初始位置设置在地图中央，但稍微分开
	soldier1.x = MAP_WIDTH / 2 - BLOCK_SIZE * 5;
	soldier1.y = MAP_HEIGHT / 2;
	soldier1.site = 2; // 初始朝向为右
	soldier1.health = 3; // 初始3条命
	soldier1.invincible = 0; // 初始不无敌
	soldier1.last_hit_time = 0;
	soldier1.is_dead = false; // 初始未死亡
	soldier1.being_rescued = false; // 初始未被救助
	soldier1.death_time = 0;
	soldier1.being_healed = false; // 初始未被治疗
	soldier1.heal_start_time = 0;
	soldier1.heal_progress = 0.0f;

	soldier2.x = MAP_WIDTH / 2 + BLOCK_SIZE * 5;
	soldier2.y = MAP_HEIGHT / 2;
	soldier2.site = 4; // 初始朝向为左
	soldier2.health = 3; // 初始3条命
	soldier2.invincible = 0; // 初始不无敌
	soldier2.last_hit_time = 0;
	soldier2.is_dead = false; // 初始未死亡
	soldier2.being_rescued = false; // 初始未被救助
	soldier2.death_time = 0;
	soldier2.being_healed = false; // 初始未被治疗
	soldier2.heal_start_time = 0;
	soldier2.heal_progress = 0.0f;

	// 初始化障碍物计数和最后生成障碍物的时间
	obstacle_count = 0;
	last_obstacle_spawn_time = GetTickCount();

	// 加载贴图 - 不切分图片，直接加载整张JPG格式图片
	// 尝试加载JPG格式图片，如果失败则尝试加载PNG格式图片
	printf("尝试加载地面贴图 'ground.jpg'...\n");

	// 尝试不同的路径组合
	const TCHAR* ground_paths[] = {
		_T("潜伏/ground.jpg"),
		_T("ground.jpg"),
		_T("./潜伏/ground.jpg"),
		_T("../潜伏/ground.jpg"),
		_T("潜伏\\ground.jpg"),
		_T("ground.png"),
		_T("潜伏/ground.png"),
		_T("./潜伏/ground.png"),
		_T("潜伏\\ground.png")
	};

	bool ground_loaded = false;
	for (size_t i = 0; i < sizeof(ground_paths) / sizeof(ground_paths[0]); i++) {
		printf("尝试路径: %s\n", ground_paths[i]);
		loadimage(&ground_texture, ground_paths[i]);
		if (ground_texture.getwidth() > 0 && ground_texture.getheight() > 0) {
			printf("成功加载地面贴图! 尺寸: %d x %d\n", ground_texture.getwidth(), ground_texture.getheight());
			ground_loaded = true;
			break;
		}
	}

	if (!ground_loaded) {
		printf("警告：无法加载地面贴图！将使用默认颜色。\n");
	}

	printf("尝试加载岩石贴图 'rock.jpg'...\n");

	// 尝试不同的路径组合
	const TCHAR* rock_paths[] = {
		_T("潜伏/rock.jpg"),
		_T("rock.jpg"),
		_T("./潜伏/rock.jpg"),
		_T("../潜伏/rock.jpg"),
		_T("潜伏\\rock.jpg"),
		_T("rock.png"),
		_T("潜伏/rock.png"),
		_T("./潜伏/rock.png"),
		_T("潜伏\\rock.png")
	};

	bool rock_loaded = false;
	for (size_t i = 0; i < sizeof(rock_paths) / sizeof(rock_paths[0]); i++) {
		printf("尝试路径: %s\n", rock_paths[i]);
		loadimage(&rock_texture, rock_paths[i]);
		if (rock_texture.getwidth() > 0 && rock_texture.getheight() > 0) {
			printf("成功加载岩石贴图! 尺寸: %d x %d\n", rock_texture.getwidth(), rock_texture.getheight());
			rock_loaded = true;
			break;
		}
	}

	if (!rock_loaded) {
		printf("警告：无法加载岩石贴图！将使用默认颜色。\n");
	}

	// 尝试加载水域贴图
	printf("尝试加载水域贴图 '水体.jpg'...\n");

	// 尝试不同的路径组合
	const TCHAR* water_paths[] = {
		_T("潜伏/水体.jpg"),
		_T("水体.jpg"),
		_T("./潜伏/水体.jpg"),
		_T("../潜伏/水体.jpg"),
		_T("潜伏\\水体.jpg"),
		_T("水体.png"),
		_T("潜伏/水体.png"),
		_T("./潜伏/水体.png"),
		_T("潜伏\\水体.png")
	};

	bool water_loaded = false;
	for (size_t i = 0; i < sizeof(water_paths) / sizeof(water_paths[0]); i++) {
		printf("尝试路径: %s\n", water_paths[i]);
		loadimage(&water_texture, water_paths[i]);
		if (water_texture.getwidth() > 0 && water_texture.getheight() > 0) {
			printf("成功加载水域贴图! 尺寸: %d x %d\n", water_texture.getwidth(), water_texture.getheight());
			water_loaded = true;
			break;
		}
	}

	if (!water_loaded) {
		printf("警告：无法加载水域贴图！将使用默认颜色。\n");
	}

	// 尝试加载警告贴图
	printf("尝试加载警告贴图 'warning.jpg'...\n");

	// 尝试不同的路径组合
	const TCHAR* warning_paths[] = {
		_T("潜伏/warning.jpg"),
		_T("warning.jpg"),
		_T("./潜伏/warning.jpg"),
		_T("../潜伏/warning.jpg"),
		_T("潜伏\\warning.jpg"),
		_T("warning.png"),
		_T("潜伏/warning.png"),
		_T("./潜伏/warning.png"),
		_T("潜伏\\warning.png")
	};

	bool warning_loaded = false;
	for (size_t i = 0; i < sizeof(warning_paths) / sizeof(warning_paths[0]); i++) {
		printf("尝试路径: %s\n", warning_paths[i]);
		loadimage(&warning_texture, warning_paths[i]);
		if (warning_texture.getwidth() > 0 && warning_texture.getheight() > 0) {
			printf("成功加载警告贴图! 尺寸: %d x %d\n", warning_texture.getwidth(), warning_texture.getheight());
			warning_loaded = true;
			break;
		}
	}

	// 如果无法从文件加载警告贴图，创建一个内置的默认警告贴图
	if (!warning_loaded) {
		printf("无法从文件加载警告贴图，创建内置默认警告贴图...\n");

		// 创建一个与方块大小相同的图像
		warning_texture.Resize(BLOCK_SIZE, BLOCK_SIZE);

		// 保存当前工作图像
		IMAGE* old_img = GetWorkingImage();

		// 设置工作图像为警告贴图
		SetWorkingImage(&warning_texture);

		// 绘制警告图案
		// 黄色背景
		setfillcolor(YELLOW);
		solidrectangle(0, 0, BLOCK_SIZE, BLOCK_SIZE);

		// 黑色警告三角形
		POINT triangle[3] = {
			{BLOCK_SIZE / 2, BLOCK_SIZE / 10},
			{BLOCK_SIZE / 10, BLOCK_SIZE * 9 / 10},
			{BLOCK_SIZE * 9 / 10, BLOCK_SIZE * 9 / 10}
		};
		setfillcolor(BLACK);
		solidpolygon(triangle, 3);

		// 黄色感叹号
		setfillcolor(YELLOW);
		solidrectangle(BLOCK_SIZE * 7 / 16, BLOCK_SIZE / 4, BLOCK_SIZE * 9 / 16, BLOCK_SIZE * 2 / 3);
		solidcircle(BLOCK_SIZE / 2, BLOCK_SIZE * 3 / 4, BLOCK_SIZE / 16);

		// 黑色边框
		setlinecolor(BLACK);
		rectangle(0, 0, BLOCK_SIZE - 1, BLOCK_SIZE - 1);

		// 恢复原工作图像
		SetWorkingImage(old_img);

		warning_loaded = true;
		printf("成功创建内置默认警告贴图! 尺寸: %d x %d\n", warning_texture.getwidth(), warning_texture.getheight());
	}

	// 创建红色警告贴图（即将爆炸）
	printf("创建红色警告贴图...\n");

	// 创建一个与方块大小相同的图像
	red_warning_texture.Resize(BLOCK_SIZE, BLOCK_SIZE);

	// 保存当前工作图像
	IMAGE* old_img = GetWorkingImage();

	// 设置工作图像为红色警告贴图
	SetWorkingImage(&red_warning_texture);

	// 绘制警告图案
	// 红色背景
	setfillcolor(RGB(255, 50, 50)); // 亮红色
	solidrectangle(0, 0, BLOCK_SIZE, BLOCK_SIZE);

	// 黑色警告三角形
	POINT triangle[3] = {
		{BLOCK_SIZE / 2, BLOCK_SIZE / 10},
		{BLOCK_SIZE / 10, BLOCK_SIZE * 9 / 10},
		{BLOCK_SIZE * 9 / 10, BLOCK_SIZE * 9 / 10}
	};
	setfillcolor(BLACK);
	solidpolygon(triangle, 3);

	// 白色感叹号（更醒目）
	setfillcolor(WHITE);
	solidrectangle(BLOCK_SIZE * 7 / 16, BLOCK_SIZE / 4, BLOCK_SIZE * 9 / 16, BLOCK_SIZE * 2 / 3);
	solidcircle(BLOCK_SIZE / 2, BLOCK_SIZE * 3 / 4, BLOCK_SIZE / 16);

	// 黑色边框
	setlinecolor(BLACK);
	rectangle(0, 0, BLOCK_SIZE - 1, BLOCK_SIZE - 1);

	// 恢复原工作图像
	SetWorkingImage(old_img);

	printf("成功创建红色警告贴图! 尺寸: %d x %d\n", red_warning_texture.getwidth(), red_warning_texture.getheight());

	// 初始化地图和迷雾
	init_map();
}

// 检查两个轰炸区域是否相连
bool are_bombing_areas_connected(BombingArea* area1, BombingCell* new_cell) {
	// 遍历area1的所有格子
	BombingCell* cell = area1->cells;
	while (cell != NULL) {
		// 检查新格子是否与area1的任何格子相邻
		if ((abs(cell->x - new_cell->x) == BLOCK_SIZE && cell->y == new_cell->y) ||
			(abs(cell->y - new_cell->y) == BLOCK_SIZE && cell->x == new_cell->x)) {
			return true;
		}
		cell = cell->next;
	}
	return false;
}

// 检查新格子是否与现有轰炸区域相连
bool is_connected_to_existing_area(int x, int y) {
	BombingArea* current_area = bombing_area_head;

	// 创建一个临时格子用于检查
	BombingCell temp_cell;
	temp_cell.x = x;
	temp_cell.y = y;

	while (current_area != NULL) {
		if (are_bombing_areas_connected(current_area, &temp_cell)) {
			return true;
		}
		current_area = current_area->next;
	}
	return false;
}

// 生成随机形状的轰炸区域
void spawn_random_bombing_area() {
	// 创建新的轰炸区域
	BombingArea* new_bombing_area = (BombingArea*)malloc(sizeof(BombingArea));
	if (new_bombing_area == NULL) {
		printf("内存分配失败！\n");
		return;
	}

	// 初始化轰炸区域
	new_bombing_area->cells = NULL;
	new_bombing_area->cell_count = 0;
	new_bombing_area->active = 1;
	new_bombing_area->marked = 0;
	new_bombing_area->create_time = GetTickCount();
	new_bombing_area->mark_time = 0;

	// 随机选择以玩家1或玩家2为中心生成轰炸区域
	Soldier* target_player = (rand() % 2 == 0) ? &soldier1 : &soldier2;
	int range = 1500; // 生成范围

	// 随机生成第一个格子的位置
	int start_x, start_y;
	bool valid_start = false;

	while (!valid_start) {
		int min_x = std::max(0, target_player->x - range);
		int max_x = std::min(MAP_WIDTH - BLOCK_SIZE, target_player->x + range);
		int min_y = std::max(0, target_player->y - range);
		int max_y = std::min(MAP_HEIGHT - BLOCK_SIZE, target_player->y + range);

		start_x = (min_x + rand() % (max_x - min_x + 1)) / BLOCK_SIZE * BLOCK_SIZE;
		start_y = (min_y + rand() % (max_y - min_y + 1)) / BLOCK_SIZE * BLOCK_SIZE;

		// 确保起始格子不与玩家、障碍物重叠，且不与现有轰炸区域相连
		valid_start = true;

		// 检查是否与玩家位置重叠或太近
		if ((abs(start_x - soldier1.x) < BLOCK_SIZE * 2 && abs(start_y - soldier1.y) < BLOCK_SIZE * 2) ||
			(abs(start_x - soldier2.x) < BLOCK_SIZE * 2 && abs(start_y - soldier2.y) < BLOCK_SIZE * 2)) {
			valid_start = false;
			continue;
		}

		// 检查是否与障碍物重叠
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL) {
			if (obs_current->x == start_x && obs_current->y == start_y) {
				valid_start = false;
				break;
			}
			obs_current = obs_current->next;
		}

		// 检查是否与现有轰炸区域相连
		if (is_connected_to_existing_area(start_x, start_y)) {
			valid_start = false;
			continue;
		}
	}

	// 添加第一个格子
	BombingCell* first_cell = (BombingCell*)malloc(sizeof(BombingCell));
	if (first_cell == NULL) {
		free(new_bombing_area);
		printf("内存分配失败！\n");
		return;
	}
	first_cell->x = start_x;
	first_cell->y = start_y;
	first_cell->next = NULL;
	new_bombing_area->cells = first_cell;
	new_bombing_area->cell_count = 1;

	// 随机生成剩余的格子（最多16个）
	int max_cells = 8 + rand() % 9; // 8到16个格子

	// 创建一个候选格子数组，用于存储可能的下一个格子位置
	typedef struct {
		int x, y;
	} CandidateCell;

	CandidateCell candidates[4 * 16]; // 最多16个格子，每个格子最多4个相邻位置
	int candidate_count = 0;

	// 初始添加第一个格子的相邻位置作为候选
	int dx[4] = {0, 1, 0, -1}; // 上、右、下、左
	int dy[4] = {-1, 0, 1, 0};

	for (int i = 0; i < 4; i++) {
		int nx = start_x + dx[i] * BLOCK_SIZE;
		int ny = start_y + dy[i] * BLOCK_SIZE;

		// 检查位置是否在地图范围内
		if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
			candidates[candidate_count].x = nx;
			candidates[candidate_count].y = ny;
			candidate_count++;
		}
	}

	// 随机添加更多格子
	while (new_bombing_area->cell_count < max_cells && candidate_count > 0) {
		// 随机选择一个候选格子
		int idx = rand() % candidate_count;
		int next_x = candidates[idx].x;
		int next_y = candidates[idx].y;

		// 检查该格子是否有效（不与玩家、障碍物重叠，不与其他轰炸区域相连）
		bool valid_cell = true;

		// 检查是否与玩家位置重叠
		if ((abs(next_x - soldier1.x) < BLOCK_SIZE && abs(next_y - soldier1.y) < BLOCK_SIZE) ||
			(abs(next_x - soldier2.x) < BLOCK_SIZE && abs(next_y - soldier2.y) < BLOCK_SIZE)) {
			valid_cell = false;
		}

		// 检查是否与障碍物重叠
		if (valid_cell) {
			Obstacle* obs_current = obstacle_head;
			while (obs_current != NULL) {
				if (obs_current->x == next_x && obs_current->y == next_y) {
					valid_cell = false;
					break;
				}
				obs_current = obs_current->next;
			}
		}

		// 检查是否与其他轰炸区域相连
		if (valid_cell && is_connected_to_existing_area(next_x, next_y)) {
			valid_cell = false;
		}

		// 检查是否已经在当前轰炸区域中
		if (valid_cell) {
			BombingCell* cell = new_bombing_area->cells;
			while (cell != NULL) {
				if (cell->x == next_x && cell->y == next_y) {
					valid_cell = false;
					break;
				}
				cell = cell->next;
			}
		}

		if (valid_cell) {
			// 添加新格子到轰炸区域
			BombingCell* new_cell = (BombingCell*)malloc(sizeof(BombingCell));
			if (new_cell == NULL) {
				printf("内存分配失败！\n");
				break;
			}
			new_cell->x = next_x;
			new_cell->y = next_y;
			new_cell->next = new_bombing_area->cells;
			new_bombing_area->cells = new_cell;
			new_bombing_area->cell_count++;

			// 添加新格子的相邻位置作为候选
			for (int i = 0; i < 4; i++) {
				int nx = next_x + dx[i] * BLOCK_SIZE;
				int ny = next_y + dy[i] * BLOCK_SIZE;

				// 检查位置是否在地图范围内
				if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
					// 添加到候选数组
					if (candidate_count < 4 * 16) {
						candidates[candidate_count].x = nx;
						candidates[candidate_count].y = ny;
						candidate_count++;
					}
				}
			}
		}

		// 移除当前候选格子
		candidates[idx] = candidates[candidate_count - 1];
		candidate_count--;
	}

	// 添加到链表头部
	new_bombing_area->next = bombing_area_head;
	bombing_area_head = new_bombing_area;

	printf("随机轰炸区域已生成！格子数量：%d\n", new_bombing_area->cell_count);
}

// 释放轰炸区域中的所有格子
void free_bombing_cells(BombingCell* head) {
	while (head != NULL) {
		BombingCell* temp = head;
		head = head->next;
		free(temp);
	}
}

void check_bombing_area() {
	// 遍历所有轰炸区域
	BombingArea* current_area = bombing_area_head;
	BombingArea* prev_area = NULL;

	while (current_area != NULL) {
		if (!current_area->active) {
			prev_area = current_area;
			current_area = current_area->next;
			continue;
		}

		// 标记轰炸区域中的每个格子为亮红色（视觉反馈）
		setfillcolor(RGB(255, 50, 50)); // 亮红色
		BombingCell* current_cell = current_area->cells;
		while (current_cell != NULL) {
			fillrectangle(current_cell->x, current_cell->y,
				current_cell->x + BLOCK_SIZE, current_cell->y + BLOCK_SIZE);
			current_cell = current_cell->next;
		}

		// 检查轰炸区域内的敌人
		Enemy* current_enemy = enemy_head;
		Enemy* prev_enemy = NULL;
		while (current_enemy != NULL) {
			// 检查敌人是否在轰炸区域内的任何格子中
			bool in_bombing_area = false;
			current_cell = current_area->cells;

			while (current_cell != NULL && !in_bombing_area) {
				// 检查敌人是否与当前格子有重叠
				if (current_enemy->x + BLOCK_SIZE > current_cell->x &&
					current_enemy->x < current_cell->x + BLOCK_SIZE &&
					current_enemy->y + BLOCK_SIZE > current_cell->y &&
					current_enemy->y < current_cell->y + BLOCK_SIZE) {
					in_bombing_area = true;
				}
				current_cell = current_cell->next;
			}

			if (in_bombing_area) {
				// 移除敌人
				if (prev_enemy == NULL) {
					enemy_head = current_enemy->next;
				} else {
					prev_enemy->next = current_enemy->next;
				}
				Enemy* temp = current_enemy;
				current_enemy = current_enemy->next;
				free(temp);
				enemy_count--; // 减少敌人计数

				// 增加玩家2得分（轰炸区消灭敌人得50分，比直接射击少）
				player2_score += 50;

				// 增加玩家2击杀计数（用于炸弹升级）
				player2_kills++;

				// 检查玩家升级
				check_player_upgrades();

				printf("敌人在轰炸中被消灭！玩家2得分增加50点，当前得分：%d\n", player2_score);
				continue;
			}
			prev_enemy = current_enemy;
			current_enemy = current_enemy->next;
		}

		// 检查轰炸区域内的玩家1
		bool player1_in_area = false;
		current_cell = current_area->cells;
		while (current_cell != NULL && !player1_in_area) {
			// 检查玩家1是否与当前格子有重叠
			if (soldier1.x + BLOCK_SIZE > current_cell->x &&
				soldier1.x < current_cell->x + BLOCK_SIZE &&
				soldier1.y + BLOCK_SIZE > current_cell->y &&
				soldier1.y < current_cell->y + BLOCK_SIZE) {
				player1_in_area = true;
			}
			current_cell = current_cell->next;
		}

		if (player1_in_area) {
			// 玩家1在轰炸区域内，扣分而不是游戏结束
			if (player1_score >= 200) {
				player1_score -= 200;
				printf("玩家1在轰炸中受伤！失去200分，当前得分：%d\n", player1_score);
			}
		}

		// 检查轰炸区域内的玩家2
		bool player2_in_area = false;
		current_cell = current_area->cells;
		while (current_cell != NULL && !player2_in_area) {
			// 检查玩家2是否与当前格子有重叠
			if (soldier2.x + BLOCK_SIZE > current_cell->x &&
				soldier2.x < current_cell->x + BLOCK_SIZE &&
				soldier2.y + BLOCK_SIZE > current_cell->y &&
				soldier2.y < current_cell->y + BLOCK_SIZE) {
				player2_in_area = true;
			}
			current_cell = current_cell->next;
		}

		if (player2_in_area) {
			// 玩家2在轰炸区域内，扣分而不是游戏结束
			if (player2_score >= 200) {
				player2_score -= 200;
				printf("玩家2在轰炸中受伤！失去200分，当前得分：%d\n", player2_score);
			}
		}

		// 检查轰炸区域内的障碍物
		Obstacle* current_obstacle = obstacle_head;
		Obstacle* prev_obstacle = NULL;
		while (current_obstacle != NULL) {
			// 检查障碍物是否在轰炸区域内的任何格子中
			bool obstacle_in_area = false;
			current_cell = current_area->cells;

			while (current_cell != NULL && !obstacle_in_area) {
				// 检查障碍物是否与当前格子有重叠
				if (current_obstacle->x + BLOCK_SIZE > current_cell->x &&
					current_obstacle->x < current_cell->x + BLOCK_SIZE &&
					current_obstacle->y + BLOCK_SIZE > current_cell->y &&
					current_obstacle->y < current_cell->y + BLOCK_SIZE) {
					obstacle_in_area = true;
				}
				current_cell = current_cell->next;
			}

			if (obstacle_in_area) {
				// 移除障碍物
				if (prev_obstacle == NULL) {
					obstacle_head = current_obstacle->next;
				} else {
					prev_obstacle->next = current_obstacle->next;
				}
				Obstacle* temp = current_obstacle;
				current_obstacle = current_obstacle->next;
				free(temp);
				obstacle_count--; // 减少障碍物计数
				printf("障碍物在轰炸中被清除！\n");
				continue;
			}
			prev_obstacle = current_obstacle;
			current_obstacle = current_obstacle->next;
		}

		// 释放轰炸区域中的所有格子，然后移除轰炸区域
		free_bombing_cells(current_area->cells);

		if (prev_area == NULL) {
			bombing_area_head = current_area->next;
			free(current_area);
			current_area = bombing_area_head;
		} else {
			prev_area->next = current_area->next;
			free(current_area);
			current_area = prev_area->next;
		}
	}

	// 清屏（移除白色标记）
	cleardevice();
}

// 游戏主循环
void game_loop() {
	spawn_obstacle(200);  // 生成障碍物

	// 用于控制敌人射击频率的计时器
	DWORD last_enemy_shot_time = GetTickCount();

	while (!game_over) {
		handle_input();

		// 检查狂暴模式状态
		check_rage_mode();

		update_enemies();
		update_bullets1();     // 更新玩家1子弹
		update_bombs();        // 更新玩家2炸弹
		update_enemy_bullets(); // 更新敌人子弹
		check_collision();

		// 检查轰炸区域
		check_bombing_areas();

		// 随机生成轰炸区域
		DWORD current_time = GetTickCount();
		if (current_time - last_bombing_check_time >= 1500) {
			// 生成轰炸区域
			spawn_random_bombing_areas(rand() % 4 + 2);
			last_bombing_check_time = current_time;
		}

		// 敌人射击逻辑
		float shot_interval = 2000.0f; // 默认2秒检查一次

		// 如果处于狂暴模式，缩短射击间隔
		if (rage_mode_active) {
			shot_interval /= get_rage_mode_multiplier(); // 根据狂暴系数缩短间隔
		}

		if (current_time - last_enemy_shot_time >= (DWORD)shot_interval) {
			// 遍历所有敌人，随机选择一些敌人射击
			Enemy* current = enemy_head;
			while (current != NULL) {
				// 计算射击概率（默认20%）
				int shoot_chance = 5;

				// 如果处于狂暴模式，增加射击概率
				if (rage_mode_active) {
					shoot_chance = (int)(5.0f / get_rage_mode_multiplier()); // 增加射击概率
					if (shoot_chance < 2) shoot_chance = 2; // 最高50%概率
				}

				// 每个敌人有一定概率射击
				if (rand() % shoot_chance == 0) {
					enemy_fire_bullet(current);
				}
				current = current->next;
			}
			last_enemy_shot_time = current_time;
		}

		draw_game();

		// 在绘制游戏画面后单独绘制狂暴模式进度条，确保它不会被其他元素覆盖
		draw_rage_mode_progress_bar();

		FlushBatchDraw(); // 刷新绘图缓冲区
		Sleep(16); // 控制帧率到约60FPS (1000/60≈16ms)

		// 随机生成敌人 - 增加刷新频率但限制总数
		static int enemy_spawn_timer = 0;
		enemy_spawn_timer++;

		// 根据当前敌人数量动态调整生成频率（提高生成速度）
		int spawn_rate;
		if (enemy_count < MAX_ENEMIES * 0.3) {
			spawn_rate = 15; // 敌人较少时，极快生成（每15帧）
		} else if (enemy_count < MAX_ENEMIES * 0.6) {
			spawn_rate = 20; // 敌人中等数量时，快速生成（每20帧）
		} else if (enemy_count < MAX_ENEMIES * 0.8) {
			spawn_rate = 25; // 敌人较多时，中速生成（每25帧）
		} else {
			spawn_rate = 30; // 敌人接近上限时，慢速生成（每30帧）
		}

		// 如果处于狂暴模式，加快敌人生成速度
		if (rage_mode_active) {
			spawn_rate = (int)(spawn_rate / get_rage_mode_multiplier());
			if (spawn_rate < 5) spawn_rate = 5; // 最快每5帧生成一个敌人
		}

		if (enemy_spawn_timer >= spawn_rate) {
			// 如果处于狂暴模式，一次生成多个敌人
			if (rage_mode_active) {
				int extra_enemies = (int)(get_rage_mode_multiplier() * 2);
				for (int i = 0; i < extra_enemies && enemy_count < MAX_ENEMIES; i++) {
					spawn_enemy(); // 会在函数内部检查是否达到最大敌人数量
				}
			} else {
				spawn_enemy(); // 正常模式下生成一个敌人
			}

			enemy_spawn_timer = 0;

			// 显示当前敌人数量
			printf("当前敌人数量: %d/%d\n", enemy_count, MAX_ENEMIES);
		}

		// 在玩家视野外随机生成障碍物
		// 使用之前已经声明的current_time变量
		if (current_time - last_obstacle_spawn_time >= 3000) { // 每3秒检查一次（原来是5秒）
			// 随机生成3-7个障碍物（原来是2-5个）
			int obstacle_spawn_count = rand() % 5 + 3;

			// 如果处于狂暴模式，增加障碍物生成数量
			if (rage_mode_active) {
				obstacle_spawn_count = (int)(obstacle_spawn_count * 1.5); // 狂暴模式下增加50%的障碍物数量
			}

			spawn_offscreen_obstacles(obstacle_spawn_count);
			last_obstacle_spawn_time = current_time;

			// 显示当前障碍物数量
			printf("当前障碍物数量: %d/%d\n", obstacle_count, MAX_OBSTACLES);
		}
	}

	EndBatchDraw(); // 结束批量绘图
	game_over_screen();
}

// 生成一组连续的水域障碍物
void spawn_water_group(int min_count) {
	// 确保至少生成min_count个连续的水域障碍物
	if (min_count < 1) min_count = 1;

	// 创建第一个水域障碍物
	Obstacle* first_water = (Obstacle*)malloc(sizeof(Obstacle));
	if (first_water == NULL) {
		printf("内存分配失败！\n");
		return;
	}

	// 随机生成水域位置（在整个地图范围内）
	first_water->x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
	first_water->y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;

	// 确保水域不会生成在两个玩家初始位置附近
	while ((abs(first_water->x - soldier1.x) < BLOCK_SIZE * 5 &&
		abs(first_water->y - soldier1.y) < BLOCK_SIZE * 5) ||
		(abs(first_water->x - soldier2.x) < BLOCK_SIZE * 5 &&
		abs(first_water->y - soldier2.y) < BLOCK_SIZE * 5)) {
		first_water->x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
		first_water->y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;
	}

	// 设置为水域类型
	first_water->type = OBSTACLE_WATER;
	first_water->next = obstacle_head;
	obstacle_head = first_water;
	obstacle_count++; // 增加障碍物计数

	// 已生成的水域数量（包括第一个）
	int water_count = 1;

	// 用于BFS的队列
	typedef struct {
		int x, y;
	} Position;

	Position queue[100]; // 假设最多100个位置
	int front = 0, rear = 0;

	// 将第一个水域的位置加入队列
	queue[rear].x = first_water->x;
	queue[rear].y = first_water->y;
	rear++;

	// 方向数组：上、右、下、左
	int dx[] = {0, BLOCK_SIZE, 0, -BLOCK_SIZE};
	int dy[] = {-BLOCK_SIZE, 0, BLOCK_SIZE, 0};

	// 使用BFS算法生成连续的水域
	while (front < rear && water_count < min_count) {
		// 从队列中取出一个位置
		Position current = queue[front++];

		// 随机打乱方向顺序，使水域形状更自然
		int directions[4] = {0, 1, 2, 3};
		for (int i = 0; i < 4; i++) {
			int j = rand() % 4;
			int temp = directions[i];
			directions[i] = directions[j];
			directions[j] = temp;
		}

		// 尝试四个方向
		for (int i = 0; i < 4 && water_count < min_count; i++) {
			int dir = directions[i];
			int new_x = current.x + dx[dir];
			int new_y = current.y + dy[dir];

			// 检查新位置是否在地图范围内
			if (new_x >= 0 && new_x < MAP_WIDTH && new_y >= 0 && new_y < MAP_HEIGHT) {
				// 检查新位置是否与玩家位置冲突
				if (!((abs(new_x - soldier1.x) < BLOCK_SIZE * 5 && abs(new_y - soldier1.y) < BLOCK_SIZE * 5) ||
					(abs(new_x - soldier2.x) < BLOCK_SIZE * 5 && abs(new_y - soldier2.y) < BLOCK_SIZE * 5))) {

					// 检查新位置是否已有障碍物
					bool has_obstacle = false;
					Obstacle* current_obs = obstacle_head;
					while (current_obs != NULL) {
						if (current_obs->x == new_x && current_obs->y == new_y) {
							has_obstacle = true;
							break;
						}
						current_obs = current_obs->next;
					}

					// 如果没有障碍物，则创建一个新的水域
					if (!has_obstacle) {
						Obstacle* new_water = (Obstacle*)malloc(sizeof(Obstacle));
						if (new_water != NULL) {
							new_water->x = new_x;
							new_water->y = new_y;
							new_water->type = OBSTACLE_WATER;
							new_water->next = obstacle_head;
							obstacle_head = new_water;
							obstacle_count++; // 增加障碍物计数
							water_count++;

							// 将新水域的位置加入队列，以便继续扩展
							queue[rear].x = new_x;
							queue[rear].y = new_y;
							rear++;
						}
					}
				}
			}
		}
	}

	printf("生成了一组连续的水域障碍物，数量：%d\n", water_count);
}

void spawn_obstacle(int count) {
	// 增加障碍物总数，使地图更加丰富
	count = (int)(count * 1.5); // 增加50%的障碍物数量

	// 首先生成更多组连续的水域障碍物（每组至少8个）
	int water_groups = count / 15; // 大约每15个障碍物中有1组水域（原来是20个）
	if (water_groups < 2) water_groups = 2; // 确保至少有2组水域（原来是1组）

	for (int i = 0; i < water_groups; i++) {
		spawn_water_group(8); // 每组至少8个连续的水域障碍物（原来是6个）
	}

	// 然后生成常规障碍物（主要是岩石）
	int remaining = count - obstacle_count;
	for (int i = 0; i < remaining; i++) {
		Obstacle* new_obstacle = (Obstacle*)malloc(sizeof(Obstacle));
		if (new_obstacle == NULL) {
			printf("内存分配失败！\n");
			exit(1);
		}

		// 随机生成障碍物位置（在整个地图范围内）
		new_obstacle->x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
		new_obstacle->y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;

		// 确保障碍物不会生成在两个玩家初始位置附近
		while ((abs(new_obstacle->x - soldier1.x) < BLOCK_SIZE * 5 &&
			abs(new_obstacle->y - soldier1.y) < BLOCK_SIZE * 5) ||
			(abs(new_obstacle->x - soldier2.x) < BLOCK_SIZE * 5 &&
			abs(new_obstacle->y - soldier2.y) < BLOCK_SIZE * 5)) {
			new_obstacle->x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
			new_obstacle->y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;
		}

		// 增加单独水域障碍物的生成概率（10%概率，原来是5%）
		new_obstacle->type = (rand() % 100 < 10) ? OBSTACLE_WATER : OBSTACLE_ROCK;

		// 将新障碍物添加到链表头部
		new_obstacle->next = obstacle_head;
		obstacle_head = new_obstacle;
		obstacle_count++; // 增加障碍物计数

		// 增加连接障碍物的生成概率（45%，原来是30%）
		if (rand() % 100 < 45) {
			// 随机决定生成1-4个额外的连接障碍物（原来是1-3个）
			int extra_count = rand() % 4 + 1;
			for (int j = 0; j < extra_count; j++) {
				// 随机选择方向：上、下、左、右
				int direction = rand() % 4;
				int offset_x = 0, offset_y = 0;

				switch (direction) {
					case 0: offset_y = -BLOCK_SIZE; break; // 上
					case 1: offset_x = BLOCK_SIZE; break;  // 右
					case 2: offset_y = BLOCK_SIZE; break;  // 下
					case 3: offset_x = -BLOCK_SIZE; break; // 左
				}

				// 计算新障碍物位置
				int new_x = new_obstacle->x + offset_x;
				int new_y = new_obstacle->y + offset_y;

				// 检查新位置是否在地图范围内
				if (new_x >= 0 && new_x < MAP_WIDTH && new_y >= 0 && new_y < MAP_HEIGHT) {
					// 检查新位置是否与玩家位置冲突
					if (!((abs(new_x - soldier1.x) < BLOCK_SIZE * 5 && abs(new_y - soldier1.y) < BLOCK_SIZE * 5) ||
						(abs(new_x - soldier2.x) < BLOCK_SIZE * 5 && abs(new_y - soldier2.y) < BLOCK_SIZE * 5))) {

						// 检查新位置是否已有障碍物
						bool has_obstacle = false;
						Obstacle* current = obstacle_head;
						while (current != NULL) {
							if (current->x == new_x && current->y == new_y) {
								has_obstacle = true;
								break;
							}
							current = current->next;
						}

						// 如果没有障碍物，则创建一个新的
						if (!has_obstacle) {
							Obstacle* extra_obstacle = (Obstacle*)malloc(sizeof(Obstacle));
							if (extra_obstacle != NULL) {
								extra_obstacle->x = new_x;
								extra_obstacle->y = new_y;
								// 连接的障碍物通常与原障碍物类型相同
								extra_obstacle->type = new_obstacle->type;
								extra_obstacle->next = obstacle_head;
								obstacle_head = extra_obstacle;
								obstacle_count++; // 增加障碍物计数
							}
						}
					}
				}
			}
		}
	}

	printf("生成了总共 %d 个障碍物，其中包含 %d 组水域\n", obstacle_count, water_groups);
}
// 绘制游戏画面
void draw_game()
{
	cleardevice();

	// 获取当前时间（用于动画效果和状态检查）
	DWORD current_time = GetTickCount();

	// 更新摄像机位置 - 跟踪两个玩家的中心点
	update_camera();

	// 在左上角显示玩家1得分和生命值
	settextcolor(WHITE);
	settextstyle(30, 0, _T("宋体")); // 更大的字体
	char score_text1[100];
	sprintf(score_text1, "玩家1(蓝)得分: %d", player1_score);
	outtextxy(20, 10, score_text1);

	// 显示玩家1生命值
	char health_text1[50];
	sprintf(health_text1, "生命: %d", soldier1.health);
	outtextxy(20, 50, health_text1);

	// 在右上角显示玩家2得分和生命值
	char score_text2[100];
	sprintf(score_text2, "玩家2(绿)得分: %d", player2_score);
	outtextxy(WINDOW_WIDTH - 400, 10, score_text2);

	// 显示玩家2生命值
	char health_text2[50];
	sprintf(health_text2, "生命: %d", soldier2.health);
	outtextxy(WINDOW_WIDTH - 400, 50, health_text2);

	// 移除此处的狂暴模式进度条绘制代码，将在游戏循环的最后单独绘制

	// 绘制地图格子 - 只绘制摄像机视野内的部分
	int start_grid_x = camera1.x / MAP_GRID_SIZE;
	int start_grid_y = camera1.y / MAP_GRID_SIZE;
	int end_grid_x = (camera1.x + WINDOW_WIDTH) / MAP_GRID_SIZE + 1;
	int end_grid_y = (camera1.y + WINDOW_HEIGHT) / MAP_GRID_SIZE + 1;

	// 限制范围
	start_grid_x = std::max(0, start_grid_x);
	start_grid_y = std::max(0, start_grid_y);
	end_grid_x = std::min(MAP_GRID_WIDTH, end_grid_x);
	end_grid_y = std::min(MAP_GRID_HEIGHT, end_grid_y);

	// 优化背景绘制 - 检查贴图是否可用（只检查一次）
	bool ground_texture_available = (ground_texture.getwidth() > 0 && ground_texture.getheight() > 0);

	// 绘制背景
	for (int x = start_grid_x; x < end_grid_x; x++) {
		for (int y = start_grid_y; y < end_grid_y; y++) {
			int screen_x = world_to_screen_x(x * MAP_GRID_SIZE);
			int screen_y = world_to_screen_y(y * MAP_GRID_SIZE);

			// 使用地面贴图绘制背景 - 直接将整张图片缩小作为贴图
			if (ground_texture_available) {
				// 使用正确的putimage重载版本 (x, y, dstwidth, dstheight, *pSrcImg, srcx, srcy, DWORD)
				putimage(screen_x, screen_y, MAP_GRID_SIZE, MAP_GRID_SIZE, &ground_texture, 0, 0, SRCCOPY);
			} else {
				// 如果贴图加载失败，使用默认颜色
				setfillcolor(RGB(200, 200, 200)); // 浅灰色背景
				fillrectangle(screen_x, screen_y, screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
			}
		}
	}

	// 优化障碍物绘制 - 预先检查贴图可用性
	bool water_texture_available = (water_texture.getwidth() > 0 && water_texture.getheight() > 0);
	bool rock_texture_available = (rock_texture.getwidth() > 0 && rock_texture.getheight() > 0);

	// 绘制障碍物（根据类型使用不同贴图）
	Obstacle* obs_current = obstacle_head;
	while (obs_current != NULL) {
		int screen_x = world_to_screen_x(obs_current->x);
		int screen_y = world_to_screen_y(obs_current->y);

		// 只绘制在屏幕范围内的障碍物
		if (screen_x >= -BLOCK_SIZE && screen_x <= WINDOW_WIDTH &&
			screen_y >= -BLOCK_SIZE && screen_y <= WINDOW_HEIGHT) {

			// 根据障碍物类型选择不同的贴图
			if (obs_current->type == OBSTACLE_WATER) {
				// 水域障碍物 - 使用水域贴图
				if (water_texture_available) {
					putimage(screen_x, screen_y, BLOCK_SIZE, BLOCK_SIZE, &water_texture, 0, 0, SRCCOPY);
				} else {
					// 如果贴图加载失败，使用默认颜色（蓝色）
					setfillcolor(RGB(0, 0, 200)); // 蓝色
					fillrectangle(screen_x, screen_y, screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);
				}
			} else {
				// 岩石障碍物 - 使用岩石贴图
				if (rock_texture_available) {
					putimage(screen_x, screen_y, BLOCK_SIZE, BLOCK_SIZE, &rock_texture, 0, 0, SRCCOPY);
				} else {
					// 如果贴图加载失败，使用默认颜色
					setfillcolor(GRAY);
					fillrectangle(screen_x, screen_y, screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);
				}
			}
		}

		obs_current = obs_current->next;
	}

	// 绘制轰炸区域
	BombingArea* bombing_current = bombing_area_head;
	while (bombing_current != NULL) {
		// 绘制轰炸区域中的每个格子
		BombingCell* cell = bombing_current->cells;
		while (cell != NULL) {
			int screen_x = world_to_screen_x(cell->x);
			int screen_y = world_to_screen_y(cell->y);

			// 检查格子是否在屏幕范围内
			if (screen_x >= -BLOCK_SIZE && screen_x <= WINDOW_WIDTH &&
				screen_y >= -BLOCK_SIZE && screen_y <= WINDOW_HEIGHT) {

				if (bombing_current->active) {
					if (bombing_current->marked) {
						// 已标记的轰炸区域（即将爆炸）- 使用红色警告贴图
						// 使用红色警告贴图
						putimage(screen_x, screen_y, BLOCK_SIZE, BLOCK_SIZE,
							&red_warning_texture, 0, 0, SRCCOPY);

						// 添加闪烁效果
						DWORD current_time = GetTickCount();
						if ((current_time / 100) % 2 == 0) {
							setlinecolor(RGB(255, 255, 255)); // 白色边框闪烁
							setlinestyle(PS_SOLID, 2);
							rectangle(screen_x, screen_y,
								screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);
							setlinestyle(PS_SOLID, 1);
						}
					} else {
						// 未标记的轰炸区域（警告阶段）- 使用警告贴图
						// 使用警告贴图（现在我们确保它总是可用的）
						putimage(screen_x, screen_y, BLOCK_SIZE, BLOCK_SIZE,
							&warning_texture, 0, 0, SRCCOPY);
					}
				} else {
					// 已轰炸的区域（减速区域）- 使用明显的紫色
					setfillcolor(RGB(180, 0, 180)); // 亮紫色
					fillrectangle(screen_x, screen_y,
						screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);

					// 为已轰炸的紫色区域添加白色边框，使其更加明显
					setlinecolor(WHITE);
					rectangle(screen_x, screen_y,
						screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);
				}
			}

			cell = cell->next;
		}

		bombing_current = bombing_current->next;
	}

	// 绘制玩家1（蓝色方块，死亡时为灰色）
	bool is_invincible1 = (current_time - soldier1.last_hit_time < (DWORD)soldier1.invincible);

	// 检查是否处于无敌状态（闪烁效果）或死亡状态
	if (!is_invincible1 || (is_invincible1 && (current_time / 100) % 2 == 0)) {
		if (soldier1.is_dead) {
			// 玩家死亡，显示为灰色
			setfillcolor(RGB(150, 150, 150)); // 灰色
			fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));

			// 添加黑色边框表示死亡
			setlinecolor(RGB(50, 50, 50)); // 深灰色/黑色
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽

			// 绘制一个"X"标记表示死亡
			line(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
			line(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y + BLOCK_SIZE),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y));

			// 如果正在被救助，添加闪烁的绿色边框
			if (soldier1.being_rescued && (current_time / 200) % 2 == 0) {
				setlinecolor(GREEN);
				setlinestyle(PS_SOLID, 3); // 更粗的边框
				rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
					world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
		} else if (is_in_purple_area(soldier1.x, soldier1.y)) {
			// 在紫色区域内，显示为非常深的蓝色并添加明显的紫色边框表示严重减速
			setfillcolor(RGB(0, 0, 100)); // 非常深的蓝色
			fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));

			// 添加更明显的紫色边框
			setlinecolor(RGB(180, 0, 180)); // 亮紫色
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽
		} else {
			// 正常显示为蓝色
			setfillcolor(BLUE);
			fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
		}

		// 如果处于无敌状态且未死亡，添加白色边框
		if (is_invincible1 && !soldier1.is_dead) {
			setlinecolor(WHITE);
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y),
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽
		}

		// 绘制玩家1的血条（在玩家上方）
		if (!soldier1.is_dead) {
			// 血条背景（灰色）
			setfillcolor(RGB(100, 100, 100));
			fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 10,
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y) - 5);

			// 血条前景（绿色，根据生命值比例显示）
			float health_ratio = (float)soldier1.health / MAX_HEALTH;
			if (health_ratio > 0) {
				// 根据血量比例改变颜色
				if (health_ratio > 0.6) {
					setfillcolor(RGB(0, 255, 0)); // 健康 - 绿色
				} else if (health_ratio > 0.3) {
					setfillcolor(RGB(255, 255, 0)); // 警告 - 黄色
				} else {
					setfillcolor(RGB(255, 0, 0)); // 危险 - 红色
				}

				fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 10,
					world_to_screen_x(soldier1.x + BLOCK_SIZE * health_ratio), world_to_screen_y(soldier1.y) - 5);
			}

			// 血条边框
			setlinecolor(BLACK);
			rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 10,
				world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y) - 5);

			// 绘制玩家1等级标识（在血条上方，不遮挡血条）
			int level_y_offset = soldier1.being_healed ? -45 : -35;

			// 等级标识背景
			setfillcolor(RGB(0, 0, 0)); // 黑色背景
			setlinecolor(RGB(255, 215, 0)); // 金色边框
			char level_label1[20];
			sprintf(level_label1, "Lv.%d", player1_level);

			settextstyle(14, 0, _T("宋体"));
			int label_width = textwidth(level_label1);
			int label_height = textheight(level_label1);
			int label_x = world_to_screen_x(soldier1.x + BLOCK_SIZE/2 - label_width/2);
			int label_y = world_to_screen_y(soldier1.y) + level_y_offset;

			fillrectangle(label_x - 2, label_y - 2, label_x + label_width + 2, label_y + label_height + 2);
			rectangle(label_x - 2, label_y - 2, label_x + label_width + 2, label_y + label_height + 2);

			// 等级文字
			settextcolor(RGB(255, 215, 0)); // 金色文字
			outtextxy(label_x, label_y, level_label1);

			// 如果正在被治疗，显示治疗进度条
			if (soldier1.being_healed) {
				// 治疗进度条背景（灰色）
				setfillcolor(RGB(100, 100, 100));
				fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 20,
					world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y) - 15);

				// 治疗进度条前景（蓝色）
				setfillcolor(RGB(0, 191, 255)); // 深天蓝色
				fillrectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 20,
					world_to_screen_x(soldier1.x + BLOCK_SIZE * soldier1.heal_progress), world_to_screen_y(soldier1.y) - 15);

				// 治疗进度条边框
				setlinecolor(BLACK);
				rectangle(world_to_screen_x(soldier1.x), world_to_screen_y(soldier1.y) - 20,
					world_to_screen_x(soldier1.x + BLOCK_SIZE), world_to_screen_y(soldier1.y) - 15);
			}
		}
	}

	// 绘制玩家2（绿色方块，死亡时为灰色）
	bool is_invincible2 = (current_time - soldier2.last_hit_time < (DWORD)soldier2.invincible);

	// 检查是否处于无敌状态（闪烁效果）或死亡状态
	if (!is_invincible2 || (is_invincible2 && (current_time / 100) % 2 == 0)) {
		if (soldier2.is_dead) {
			// 玩家死亡，显示为灰色
			setfillcolor(RGB(150, 150, 150)); // 灰色
			fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));

			// 添加黑色边框表示死亡
			setlinecolor(RGB(50, 50, 50)); // 深灰色/黑色
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽

			// 绘制一个"X"标记表示死亡
			line(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
			line(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y + BLOCK_SIZE),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y));

			// 如果正在被救助，添加闪烁的绿色边框
			if (soldier2.being_rescued && (current_time / 200) % 2 == 0) {
				setlinecolor(GREEN);
				setlinestyle(PS_SOLID, 3); // 更粗的边框
				rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
					world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
		} else if (is_in_purple_area(soldier2.x, soldier2.y)) {
			// 在紫色区域内，显示为非常深的绿色并添加明显的紫色边框表示严重减速
			setfillcolor(RGB(0, 100, 0)); // 非常深的绿色
			fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));

			// 添加更明显的紫色边框
			setlinecolor(RGB(180, 0, 180)); // 亮紫色
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽
		} else {
			// 正常显示为绿色
			setfillcolor(GREEN);
			fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
		}

		// 如果处于无敌状态且未死亡，添加白色边框
		if (is_invincible2 && !soldier2.is_dead) {
			setlinecolor(WHITE);
			setlinestyle(PS_SOLID, 2); // 加粗边框
			rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y),
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y + BLOCK_SIZE));
			setlinestyle(PS_SOLID, 1); // 恢复默认线宽
		}

		// 绘制玩家2的血条（在玩家上方）
		if (!soldier2.is_dead) {
			// 血条背景（灰色）
			setfillcolor(RGB(100, 100, 100));
			fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 10,
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y) - 5);

			// 血条前景（绿色，根据生命值比例显示）
			float health_ratio = (float)soldier2.health / MAX_HEALTH;
			if (health_ratio > 0) {
				// 根据血量比例改变颜色
				if (health_ratio > 0.6) {
					setfillcolor(RGB(0, 255, 0)); // 健康 - 绿色
				} else if (health_ratio > 0.3) {
					setfillcolor(RGB(255, 255, 0)); // 警告 - 黄色
				} else {
					setfillcolor(RGB(255, 0, 0)); // 危险 - 红色
				}

				fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 10,
					world_to_screen_x(soldier2.x + BLOCK_SIZE * health_ratio), world_to_screen_y(soldier2.y) - 5);
			}

			// 血条边框
			setlinecolor(BLACK);
			rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 10,
				world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y) - 5);

			// 绘制玩家2等级标识（在血条上方，不遮挡血条）
			int level_y_offset2 = soldier2.being_healed ? -45 : -35;

			// 等级标识背景
			setfillcolor(RGB(0, 0, 0)); // 黑色背景
			setlinecolor(RGB(255, 215, 0)); // 金色边框
			char level_label2[20];
			sprintf(level_label2, "Lv.%d", player2_level);

			settextstyle(14, 0, _T("宋体"));
			int label_width2 = textwidth(level_label2);
			int label_height2 = textheight(level_label2);
			int label_x2 = world_to_screen_x(soldier2.x + BLOCK_SIZE/2 - label_width2/2);
			int label_y2 = world_to_screen_y(soldier2.y) + level_y_offset2;

			fillrectangle(label_x2 - 2, label_y2 - 2, label_x2 + label_width2 + 2, label_y2 + label_height2 + 2);
			rectangle(label_x2 - 2, label_y2 - 2, label_x2 + label_width2 + 2, label_y2 + label_height2 + 2);

			// 等级文字
			settextcolor(RGB(255, 215, 0)); // 金色文字
			outtextxy(label_x2, label_y2, level_label2);

			// 如果正在被治疗，显示治疗进度条
			if (soldier2.being_healed) {
				// 治疗进度条背景（灰色）
				setfillcolor(RGB(100, 100, 100));
				fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 20,
					world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y) - 15);

				// 治疗进度条前景（蓝色）
				setfillcolor(RGB(0, 191, 255)); // 深天蓝色
				fillrectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 20,
					world_to_screen_x(soldier2.x + BLOCK_SIZE * soldier2.heal_progress), world_to_screen_y(soldier2.y) - 15);

				// 治疗进度条边框
				setlinecolor(BLACK);
				rectangle(world_to_screen_x(soldier2.x), world_to_screen_y(soldier2.y) - 20,
					world_to_screen_x(soldier2.x + BLOCK_SIZE), world_to_screen_y(soldier2.y) - 15);
			}
		}
	}

	// 绘制敌人（红色方块）- 在两个屏幕上都显示
	Enemy* current = enemy_head;

	// 预计算常用颜色以提高性能
	static COLORREF sniper_color = RGB(255, 165, 0); // 橙色
	static COLORREF tank_color = RGB(139, 0, 0); // 深红色
	static COLORREF normal_color = RGB(255, 0, 0); // 红色
	static COLORREF purple_border = RGB(180, 0, 180); // 亮紫色

	while (current != NULL) {
		// 绘制在左半屏幕（玩家1视角）
		int screen_x1 = world_to_screen_x1(current->x);
		int screen_y1 = world_to_screen_y1(current->y);

		if (screen_x1 >= -BLOCK_SIZE && screen_x1 <= WINDOW_WIDTH / 2 &&
			screen_y1 >= -BLOCK_SIZE && screen_y1 <= WINDOW_HEIGHT) {

			// 检查敌人是否在玩家视野内
			bool in_player_view = is_in_player_view(current->x, current->y);

			// 根据敌人类型选择基础颜色（使用预计算的颜色）
			COLORREF enemy_color;
			if (current->type == ENEMY_SNIPER) {
				enemy_color = sniper_color;
			} else if (current->type == ENEMY_TANK) {
				enemy_color = tank_color;
			} else {
				enemy_color = normal_color;
			}

			// 然后根据敌人状态调整颜色
			if (is_in_purple_area(current->x, current->y)) {
				// 在紫色区域内，显示为更深的颜色并添加明显的紫色边框表示严重减速
				// 将颜色调暗（优化：减少RGB分量提取操作）
				setfillcolor(RGB(GetRValue(enemy_color) / 2, GetGValue(enemy_color) / 2, GetBValue(enemy_color) / 2));

				fillrectangle(screen_x1, screen_y1,
					screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE);

				// 添加更明显的紫色边框
				setlinecolor(purple_border);
				setlinestyle(PS_SOLID, 2); // 加粗边框
				rectangle(screen_x1, screen_y1,
					screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE);
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
			else if (!in_player_view) {
				// 不在玩家视野中，显示为闪烁效果表示速度更快
				// 优化：使用当前时间变量而不是重复调用GetTickCount()
				if ((current_time / 200) % 2 == 0) {
					setfillcolor(enemy_color);
				} else {
					// 闪烁时稍微调暗（优化：直接计算而不是分别提取RGB）
					setfillcolor(RGB((GetRValue(enemy_color) * 4) / 5,
									(GetGValue(enemy_color) * 4) / 5,
									(GetBValue(enemy_color) * 4) / 5));
				}
				fillrectangle(screen_x1, screen_y1,
					screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE);

				// 添加黑色边框
				setlinecolor(BLACK);
				setlinestyle(PS_SOLID, 2); // 加粗边框
				rectangle(screen_x1, screen_y1,
					screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE);
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
			else {
				// 正常显示
				setfillcolor(enemy_color);
				fillrectangle(screen_x1, screen_y1,
					screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE);
			}

			// 为坦克敌人显示生命值
			if (current->type == ENEMY_TANK) {
				// 在敌人上方显示生命值指示器
				for (int i = 0; i < current->health; i++) {
					setfillcolor(RGB(255, 255, 255)); // 白色
					fillrectangle(screen_x1 + i * (BLOCK_SIZE / 3), screen_y1 - 5,
						screen_x1 + (i + 1) * (BLOCK_SIZE / 3) - 1, screen_y1 - 2);
				}
			}

			// 为狙击手敌人添加特殊标记
			if (current->type == ENEMY_SNIPER) {
				// 在敌人上绘制一个十字准星
				setlinecolor(RGB(255, 255, 255)); // 白色
				line(screen_x1 + BLOCK_SIZE / 2, screen_y1,
					 screen_x1 + BLOCK_SIZE / 2, screen_y1 + BLOCK_SIZE);
				line(screen_x1, screen_y1 + BLOCK_SIZE / 2,
					 screen_x1 + BLOCK_SIZE, screen_y1 + BLOCK_SIZE / 2);
			}
		}

		// 绘制在右半屏幕（玩家2视角）
		int screen_x2 = world_to_screen_x2(current->x);
		int screen_y2 = world_to_screen_y2(current->y);

		if (screen_x2 >= WINDOW_WIDTH / 2 && screen_x2 <= WINDOW_WIDTH &&
			screen_y2 >= -BLOCK_SIZE && screen_y2 <= WINDOW_HEIGHT) {

			// 检查敌人是否在玩家视野内
			bool in_player_view = is_in_player_view(current->x, current->y);

			// 根据敌人类型和状态设置不同的颜色
			COLORREF enemy_color;

			// 首先根据敌人类型选择基础颜色
			if (current->type == ENEMY_SNIPER) {
				// 狙击手敌人 - 橙色
				enemy_color = RGB(255, 165, 0); // 橙色
			} else if (current->type == ENEMY_TANK) {
				// 坦克敌人 - 深红色
				enemy_color = RGB(139, 0, 0); // 深红色
			} else {
				// 普通敌人 - 红色
				enemy_color = RGB(255, 0, 0); // 红色
			}

			// 然后根据敌人状态调整颜色
			if (is_in_purple_area(current->x, current->y)) {
				// 在紫色区域内，显示为更深的颜色并添加明显的紫色边框表示严重减速
				// 将颜色调暗
				int r = GetRValue(enemy_color) / 2;
				int g = GetGValue(enemy_color) / 2;
				int b = GetBValue(enemy_color) / 2;
				setfillcolor(RGB(r, g, b));

				fillrectangle(screen_x2, screen_y2,
					screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE);

				// 添加更明显的紫色边框
				setlinecolor(RGB(180, 0, 180)); // 亮紫色
				setlinestyle(PS_SOLID, 2); // 加粗边框
				rectangle(screen_x2, screen_y2,
					screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE);
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
			else if (!in_player_view) {
				// 不在玩家视野中，显示为闪烁效果表示速度更快
				if ((GetTickCount() / 200) % 2 == 0) {
					setfillcolor(enemy_color);
				} else {
					// 闪烁时稍微调暗
					int r = GetRValue(enemy_color) * 0.8;
					int g = GetGValue(enemy_color) * 0.8;
					int b = GetBValue(enemy_color) * 0.8;
					setfillcolor(RGB(r, g, b));
				}
				fillrectangle(screen_x2, screen_y2,
					screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE);

				// 添加黑色边框
				setlinecolor(BLACK);
				setlinestyle(PS_SOLID, 2); // 加粗边框
				rectangle(screen_x2, screen_y2,
					screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE);
				setlinestyle(PS_SOLID, 1); // 恢复默认线宽
			}
			else {
				// 正常显示
				setfillcolor(enemy_color);
				fillrectangle(screen_x2, screen_y2,
					screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE);
			}

			// 为坦克敌人显示生命值
			if (current->type == ENEMY_TANK) {
				// 在敌人上方显示生命值指示器
				for (int i = 0; i < current->health; i++) {
					setfillcolor(RGB(255, 255, 255)); // 白色
					fillrectangle(screen_x2 + i * (BLOCK_SIZE / 3), screen_y2 - 5,
						screen_x2 + (i + 1) * (BLOCK_SIZE / 3) - 1, screen_y2 - 2);
				}
			}

			// 为狙击手敌人添加特殊标记
			if (current->type == ENEMY_SNIPER) {
				// 在敌人上绘制一个十字准星
				setlinecolor(RGB(255, 255, 255)); // 白色
				line(screen_x2 + BLOCK_SIZE / 2, screen_y2,
					 screen_x2 + BLOCK_SIZE / 2, screen_y2 + BLOCK_SIZE);
				line(screen_x2, screen_y2 + BLOCK_SIZE / 2,
					 screen_x2 + BLOCK_SIZE, screen_y2 + BLOCK_SIZE / 2);
			}
		}

		current = current->next;
	}

	// 绘制玩家1子弹 - 在整个屏幕上
	Bullet* bullet_current1 = bullet_head1;
	while (bullet_current1 != NULL) {
		if (bullet_current1->active) {
			// 计算子弹在屏幕上的位置
			int screen_x = world_to_screen_x(bullet_current1->x);
			int screen_y = world_to_screen_y(bullet_current1->y);

			// 检查子弹是否在屏幕范围内（不再限制为左半屏幕）
			if (screen_x >= -BLOCK_SIZE/2 && screen_x <= WINDOW_WIDTH &&
				screen_y >= -BLOCK_SIZE/2 && screen_y <= WINDOW_HEIGHT) {

				// 绘制子弹
				setfillcolor(YELLOW);
				fillrectangle(screen_x, screen_y,
					screen_x + BLOCK_SIZE/2, screen_y + BLOCK_SIZE/2);
			}
		}
		bullet_current1 = bullet_current1->next;
	}

	// 绘制玩家2的炸弹
	draw_bombs();

	// 绘制敌人子弹 - 在两个屏幕上都显示
	EnemyBullet* enemy_bullet_current = enemy_bullet_head;
	while (enemy_bullet_current != NULL) {
		if (enemy_bullet_current->active) {
			// 左半屏幕（玩家1视角）
			int screen_x1 = world_to_screen_x1(enemy_bullet_current->x);
			int screen_y1 = world_to_screen_y1(enemy_bullet_current->y);

			if (screen_x1 >= -BLOCK_SIZE/2 && screen_x1 <= WINDOW_WIDTH / 2 &&
				screen_y1 >= -BLOCK_SIZE/2 && screen_y1 <= WINDOW_HEIGHT) {

				// 根据子弹类型设置不同的颜色
				if (enemy_bullet_current->type == ENEMY_SNIPER) {
					// 狙击手子弹 - 亮橙色，更大
					setfillcolor(RGB(255, 200, 0)); // 亮橙色
					fillrectangle(screen_x1, screen_y1,
						screen_x1 + BLOCK_SIZE/2 + 2, screen_y1 + BLOCK_SIZE/2 + 2);
				} else if (enemy_bullet_current->type == ENEMY_TANK) {
					// 坦克子弹 - 深红色，更大
					setfillcolor(RGB(180, 0, 0)); // 深红色
					fillrectangle(screen_x1, screen_y1,
						screen_x1 + BLOCK_SIZE/2 + 4, screen_y1 + BLOCK_SIZE/2 + 4);
				} else {
					// 普通敌人子弹 - 浅红色
					setfillcolor(RGB(255, 100, 100)); // 浅红色
					fillrectangle(screen_x1, screen_y1,
						screen_x1 + BLOCK_SIZE/2, screen_y1 + BLOCK_SIZE/2);
				}
			}

			// 右半屏幕（玩家2视角）
			int screen_x2 = world_to_screen_x2(enemy_bullet_current->x);
			int screen_y2 = world_to_screen_y2(enemy_bullet_current->y);

			if (screen_x2 >= WINDOW_WIDTH / 2 && screen_x2 <= WINDOW_WIDTH &&
				screen_y2 >= -BLOCK_SIZE/2 && screen_y2 <= WINDOW_HEIGHT) {

				// 根据子弹类型设置不同的颜色
				if (enemy_bullet_current->type == ENEMY_SNIPER) {
					// 狙击手子弹 - 亮橙色，更大
					setfillcolor(RGB(255, 200, 0)); // 亮橙色
					fillrectangle(screen_x2, screen_y2,
						screen_x2 + BLOCK_SIZE/2 + 2, screen_y2 + BLOCK_SIZE/2 + 2);
				} else if (enemy_bullet_current->type == ENEMY_TANK) {
					// 坦克子弹 - 深红色，更大
					setfillcolor(RGB(180, 0, 0)); // 深红色
					fillrectangle(screen_x2, screen_y2,
						screen_x2 + BLOCK_SIZE/2 + 4, screen_y2 + BLOCK_SIZE/2 + 4);
				} else {
					// 普通敌人子弹 - 浅红色
					setfillcolor(RGB(255, 100, 100)); // 浅红色
					fillrectangle(screen_x2, screen_y2,
						screen_x2 + BLOCK_SIZE/2, screen_y2 + BLOCK_SIZE/2);
				}
			}
		}
		enemy_bullet_current = enemy_bullet_current->next;
	}

	// 绘制两个玩家的小地图
	draw_minimap1(); // 玩家1的小地图（左上角）
	draw_minimap2(); // 玩家2的小地图（右上角）

	// 结束批量绘图以提高性能
	EndBatchDraw();
}

void spawn_enemy()
{
	// 检查是否已达到最大敌人数量
	if (enemy_count >= MAX_ENEMIES) {
		printf("已达到最大敌人数量 (%d)，不再生成新敌人\n", MAX_ENEMIES);
		return;
	}

	int valid_position = 0;
	while (!valid_position) {
		// 随机生成敌人位置
		Enemy* new_enemy = (Enemy*)malloc(sizeof(Enemy));
		if (new_enemy == NULL) {
			printf("内存分配失败！\n");
			exit(1);
		}

		// 在两个玩家周围一定范围内生成敌人，而不是整个地图
		int range = 2000; // 生成范围

		// 随机选择以玩家1或玩家2为中心生成敌人
		Soldier* target_player = (rand() % 2 == 0) ? &soldier1 : &soldier2;

		int min_x = std::max(0, target_player->x - range);
		int max_x = std::min(MAP_WIDTH - BLOCK_SIZE, target_player->x + range);
		int min_y = std::max(0, target_player->y - range);
		int max_y = std::min(MAP_HEIGHT - BLOCK_SIZE, target_player->y + range);

		new_enemy->x = (min_x + rand() % (max_x - min_x + 1)) / BLOCK_SIZE * BLOCK_SIZE;
		new_enemy->y = (min_y + rand() % (max_y - min_y + 1)) / BLOCK_SIZE * BLOCK_SIZE;

		// 确保敌人不会生成在玩家位置、障碍物位置
		valid_position = 1; // 假设初始合法

		// 检查是否与玩家1位置太近
		if (abs(new_enemy->x - soldier1.x) < BLOCK_SIZE * 5 &&
			abs(new_enemy->y - soldier1.y) < BLOCK_SIZE * 5) {
			valid_position = 0;
			free(new_enemy);
			continue;
		}

		// 检查是否与玩家2位置太近
		if (abs(new_enemy->x - soldier2.x) < BLOCK_SIZE * 5 &&
			abs(new_enemy->y - soldier2.y) < BLOCK_SIZE * 5) {
			valid_position = 0;
			free(new_enemy);
			continue;
		}

		// 检查是否与障碍物重叠
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL) {
			if (new_enemy->x == obs_current->x && new_enemy->y == obs_current->y) {
				valid_position = 0;
				break;
			}
			obs_current = obs_current->next;
		}

		// 如果位置合法，初始化敌人属性并将其添加到链表头部
		if (valid_position) {
			// 初始化新增字段
			new_enemy->target_player = -1; // 初始未设置目标玩家
			new_enemy->frame_count = 0;    // 初始帧计数为0
			new_enemy->last_attack_time = GetTickCount(); // 初始化上次攻击时间

			// 随机决定敌人类型
			int enemy_type_rand = rand() % 100;
			if (enemy_type_rand < 15) { // 15%的概率生成狙击手（从10%提高到15%）
				new_enemy->type = ENEMY_SNIPER;
				new_enemy->health = 1; // 狙击手只有1点生命值
				printf("生成了一个狙击手敌人！\n");
			} else if (enemy_type_rand < 25) { // 10%的概率生成坦克（从15%降低到10%）
				new_enemy->type = ENEMY_TANK;
				new_enemy->health = 3; // 坦克有3点生命值
				printf("生成了一个坦克敌人！\n");
			} else { // 75%的概率生成普通敌人（保持不变）
				new_enemy->type = ENEMY_NORMAL;
				new_enemy->health = 1; // 普通敌人只有1点生命值
			}

			new_enemy->next = enemy_head;
			enemy_head = new_enemy;
			enemy_count++; // 增加敌人计数

			// 根据敌人类型输出不同的生成信息
			if (new_enemy->type == ENEMY_SNIPER) {
				printf("狙击手敌人在 (%d, %d) 生成，当前敌人数量: %d/%d\n", new_enemy->x, new_enemy->y, enemy_count, MAX_ENEMIES);
			} else if (new_enemy->type == ENEMY_TANK) {
				printf("坦克敌人在 (%d, %d) 生成，当前敌人数量: %d/%d\n", new_enemy->x, new_enemy->y, enemy_count, MAX_ENEMIES);
			} else {
				printf("普通敌人在 (%d, %d) 生成，当前敌人数量: %d/%d\n", new_enemy->x, new_enemy->y, enemy_count, MAX_ENEMIES);
			}
		} else {
			free(new_enemy);
		}
	}
}

// 这个函数已经被update_bullets1和update_bombs替代，保留此函数仅为兼容性
void update_bullets() {
	// 调用新的子弹更新函数
	update_bullets1();
	update_bombs(); // 玩家2现在使用炸弹而不是子弹
}

// 这个函数已经被draw_bullets1和draw_bombs替代，保留此函数仅为兼容性
void draw_bullets() {
	// 调用新的子弹绘制函数
	draw_bullets1();
	draw_bombs(); // 玩家2现在使用炸弹而不是子弹
}

// 更新敌人移动逻辑
void update_enemies() {
	Enemy* current = enemy_head;
	int target_x, target_y;
	while (current != NULL) {
		// 增加帧计数
		current->frame_count++;

		// 检查玩家存活状态，选择追踪目标
		bool player1_alive = !soldier1.is_dead;
		bool player2_alive = !soldier2.is_dead;

		// 如果两个玩家都死亡，敌人随机移动
		if (!player1_alive && !player2_alive) {
			// 随机移动方向
			if (current->frame_count % 30 == 0) { // 每30帧改变一次方向
				current->target_player = rand() % 4 + 2; // 2-5表示四个随机方向
			}

			// 根据随机方向设置目标坐标
			int random_distance = 200; // 随机移动的距离
			switch (current->target_player) {
				case 2: // 上
					target_x = current->x;
					target_y = current->y - random_distance;
					break;
				case 3: // 右
					target_x = current->x + random_distance;
					target_y = current->y;
					break;
				case 4: // 下
					target_x = current->x;
					target_y = current->y + random_distance;
					break;
				case 5: // 左
					target_x = current->x - random_distance;
					target_y = current->y;
					break;
				default:
					target_x = current->x;
					target_y = current->y;
			}
		}
		// 如果只有一个玩家存活，敌人将追踪该玩家
		else if (player1_alive && !player2_alive) {
			current->target_player = 0; // 追踪玩家1
			target_x = soldier1.x + BLOCK_SIZE / 2;
			target_y = soldier1.y + BLOCK_SIZE / 2;
		}
		else if (!player1_alive && player2_alive) {
			current->target_player = 1; // 追踪玩家2
			target_x = soldier2.x + BLOCK_SIZE / 2;
			target_y = soldier2.y + BLOCK_SIZE / 2;
		}
		// 如果两个玩家都存活，随机选择追踪目标
		else {
			// 每个敌人有自己的目标，并且每10帧有10%的概率改变目标
			if (current->target_player == -1 || current->target_player > 1 ||
				(rand() % 100 < 10 && current->frame_count % 10 == 0)) {
				current->target_player = rand() % 2; // 0表示玩家1，1表示玩家2
			}

			// 根据目标玩家设置目标坐标
			if (current->target_player == 0) {
				// 敌人朝玩家1移动
				target_x = soldier1.x + BLOCK_SIZE / 2;
				target_y = soldier1.y + BLOCK_SIZE / 2;
			} else {
				// 敌人朝玩家2移动
				target_x = soldier2.x + BLOCK_SIZE / 2;
				target_y = soldier2.y + BLOCK_SIZE / 2;
			}
		}

		// 计算方向向量
		int dx = target_x - (current->x + BLOCK_SIZE / 2);
		int dy = target_y - (current->y + BLOCK_SIZE / 2);
		double dist = sqrt(dx * dx + dy * dy);

		// 检查敌人是否在紫色区域内，如果是则降低移速
		float speed_multiplier = 1.0f;

		// 检查是否处于狂暴模式，如果是则提高移速
		if (rage_mode_active) {
			speed_multiplier = get_rage_mode_multiplier(); // 根据狂暴系数提高速度
		}

		// 检查敌人是否在玩家视野内
		bool in_player_view = is_in_player_view(current->x, current->y);

		// 应用其他速度修饰符
		if (is_in_purple_area(current->x, current->y)) {
			speed_multiplier *= 0.25f; // 在紫色区域内移速降低为当前的25%（四分之一）
			// 即使在狂暴模式下，紫色区域仍然会大幅降低敌人速度
		}
		else if (!in_player_view) {
			// 敌人不在玩家视野中，速度提高
			speed_multiplier *= 1.5f; // 视野外移速提高为当前的150%
		}

		if (dist > 0) {
			dx = (int)((double)dx / dist * ENEMY_SPEED * speed_multiplier);
			dy = (int)((double)dy / dist * ENEMY_SPEED * speed_multiplier);
		}

		// 更新敌人位置
		int new_x = current->x + dx;
		int new_y = current->y + dy;

		// 边界检查 - 使用地图边界而不是窗口边界
		if (new_x < 0) new_x = 0;
		if (new_x > MAP_WIDTH - BLOCK_SIZE) new_x = MAP_WIDTH - BLOCK_SIZE;
		if (new_y < 0) new_y = 0;
		if (new_y > MAP_HEIGHT - BLOCK_SIZE) new_y = MAP_HEIGHT - BLOCK_SIZE;

		// 障碍物碰撞检测 - 防止敌人穿过障碍物
		if (!obstacle_collision(new_x, new_y)) {
			// 只有在没有障碍物的情况下才更新敌人位置
			current->x = new_x;
			current->y = new_y;
		} else {
			// 如果遇到障碍物，尝试沿着障碍物移动（智能避障）
			// 首先尝试水平移动
			if (dx != 0) {
				new_x = current->x;
				if (!obstacle_collision(new_x, new_y)) {
					current->y = new_y; // 可以垂直移动
				}
			}
			// 然后尝试垂直移动
			if (dy != 0) {
				new_y = current->y;
				if (!obstacle_collision(new_x, new_y)) {
					current->x = new_x; // 可以水平移动
				}
			}
			// 如果两个方向都不能移动，敌人将保持原位
		}

		current = current->next;
	}
}

// 碰撞检测
void check_collision() {
	// 检测玩家1与敌人的碰撞（只有在玩家1未死亡时才检测）
	DWORD current_time = GetTickCount();
	Enemy* current = enemy_head;
	while (current != NULL) {
		if (!soldier1.is_dead &&
			soldier1.x < current->x + BLOCK_SIZE &&
			soldier1.x + BLOCK_SIZE > current->x &&
			soldier1.y < current->y + BLOCK_SIZE &&
			soldier1.y + BLOCK_SIZE > current->y) {

			// 检查玩家1是否处于无敌状态
			if (current_time - soldier1.last_hit_time >= (DWORD)soldier1.invincible) {
				// 玩家1受伤，减少生命值
				soldier1.health--;
				printf("玩家1与敌人碰撞，失去1条生命！剩余生命：%d\n", soldier1.health);

				// 设置无敌时间（2秒）
				soldier1.invincible = 2000;
				soldier1.last_hit_time = current_time;

				// 如果生命值为0，玩家死亡但游戏不结束
				if (soldier1.health <= 0 && !soldier1.is_dead) {
					printf("玩家1死亡，等待救援！\n");
					soldier1.is_dead = true;
					soldier1.death_time = current_time;

					// 只有当两个玩家都死亡时，游戏才结束
					if (soldier1.is_dead && soldier2.is_dead) {
						printf("两名玩家均已阵亡，游戏结束！\n");
						game_over = 1;
					}
				}

				// 扣分
				if (player1_score >= 100) {
					player1_score -= 100;
					printf("玩家1失去100分！当前得分：%d\n", player1_score);
				}
			}

			// 敌人不再被移除，而是将玩家击退一段距离
			// 计算击退方向（从敌人到玩家的方向）
			int knockback_x = soldier1.x - current->x;
			int knockback_y = soldier1.y - current->y;

			// 标准化方向向量
			double length = sqrt(knockback_x * knockback_x + knockback_y * knockback_y);
			if (length > 0) {
				knockback_x = (int)(knockback_x / length * BLOCK_SIZE * 2); // 击退距离为2个方块
				knockback_y = (int)(knockback_y / length * BLOCK_SIZE * 2);
			}

			// 应用击退效果
			int new_x = soldier1.x + knockback_x;
			int new_y = soldier1.y + knockback_y;

			// 确保不会击退到地图外
			if (new_x < 0) new_x = 0;
			if (new_x > MAP_WIDTH - BLOCK_SIZE) new_x = MAP_WIDTH - BLOCK_SIZE;
			if (new_y < 0) new_y = 0;
			if (new_y > MAP_HEIGHT - BLOCK_SIZE) new_y = MAP_HEIGHT - BLOCK_SIZE;

			// 确保不会击退到障碍物中
			if (!obstacle_collision(new_x, new_y)) {
				soldier1.x = new_x;
				soldier1.y = new_y;
			}

			// 继续检查下一个敌人
			current = current->next;
		}
		current = current->next;
	}

	// 检测玩家2与敌人的碰撞（只有在玩家2未死亡时才检测）
	current = enemy_head;
	while (current != NULL) {
		if (!soldier2.is_dead &&
			soldier2.x < current->x + BLOCK_SIZE &&
			soldier2.x + BLOCK_SIZE > current->x &&
			soldier2.y < current->y + BLOCK_SIZE &&
			soldier2.y + BLOCK_SIZE > current->y) {

			// 检查玩家2是否处于无敌状态
			if (current_time - soldier2.last_hit_time >= soldier2.invincible) {
				// 玩家2受伤，减少生命值
				soldier2.health--;
				printf("玩家2与敌人碰撞，失去1条生命！剩余生命：%d\n", soldier2.health);

				// 设置无敌时间（2秒）
				soldier2.invincible = 2000;
				soldier2.last_hit_time = current_time;

				// 如果生命值为0，玩家死亡但游戏不结束
				if (soldier2.health <= 0 && !soldier2.is_dead) {
					printf("玩家2死亡，等待救援！\n");
					soldier2.is_dead = true;
					soldier2.death_time = current_time;

					// 只有当两个玩家都死亡时，游戏才结束
					if (soldier1.is_dead && soldier2.is_dead) {
						printf("两名玩家均已阵亡，游戏结束！\n");
						game_over = 1;
					}
				}

				// 扣分
				if (player2_score >= 100) {
					player2_score -= 100;
					printf("玩家2失去100分！当前得分：%d\n", player2_score);
				}
			}

			// 敌人不再被移除，而是将玩家击退一段距离
			// 计算击退方向（从敌人到玩家的方向）
			int knockback_x = soldier2.x - current->x;
			int knockback_y = soldier2.y - current->y;

			// 标准化方向向量
			double length = sqrt(knockback_x * knockback_x + knockback_y * knockback_y);
			if (length > 0) {
				knockback_x = (int)(knockback_x / length * BLOCK_SIZE * 2); // 击退距离为2个方块
				knockback_y = (int)(knockback_y / length * BLOCK_SIZE * 2);
			}

			// 应用击退效果
			int new_x = soldier2.x + knockback_x;
			int new_y = soldier2.y + knockback_y;

			// 确保不会击退到地图外
			if (new_x < 0) new_x = 0;
			if (new_x > MAP_WIDTH - BLOCK_SIZE) new_x = MAP_WIDTH - BLOCK_SIZE;
			if (new_y < 0) new_y = 0;
			if (new_y > MAP_HEIGHT - BLOCK_SIZE) new_y = MAP_HEIGHT - BLOCK_SIZE;

			// 确保不会击退到障碍物中
			if (!obstacle_collision(new_x, new_y)) {
				soldier2.x = new_x;
				soldier2.y = new_y;
			}

			// 继续检查下一个敌人
			current = current->next;
		}
		current = current->next;
	}
}

// 游戏结束屏幕
void game_over_screen() {
	cleardevice();
	settextcolor(RED);
	settextstyle(40, 0, _T("宋体"));
	outtextxy(WINDOW_WIDTH / 2 - 100, WINDOW_HEIGHT / 2 - 150, "游戏结束！");

	// 显示得分和生命值
	char score_text[50];
	settextstyle(30, 0, _T("宋体"));

	// 玩家1信息
	sprintf(score_text, "玩家1(蓝)得分: %d", player1_score);
	outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 - 80, score_text);

	sprintf(score_text, "剩余生命: %d", soldier1.health);
	outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 - 40, score_text);

	// 玩家2信息
	sprintf(score_text, "玩家2(绿)得分: %d", player2_score);
	outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 + 20, score_text);

	sprintf(score_text, "剩余生命: %d", soldier2.health);
	outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 + 60, score_text);

	// 显示胜利者
	settextstyle(35, 0, _T("宋体"));
	settextcolor(YELLOW);

	// 判断胜利条件：
	// 1. 如果两个玩家都死亡，显示游戏结束
	// 2. 如果一个玩家死亡，另一个玩家胜利
	// 3. 如果两个玩家都活着，比较得分
	if (soldier1.is_dead && soldier2.is_dead) {
		settextcolor(RED);
		settextstyle(40, 0, _T("宋体"));
		outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 120, "两名玩家均已阵亡！游戏结束！");

		// 显示最终得分比较
		settextstyle(30, 0, _T("宋体"));
		settextcolor(YELLOW);
		if (player1_score > player2_score) {
			outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 170, "玩家1得分更高！");
		} else if (player2_score > player1_score) {
			outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 170, "玩家2得分更高！");
		} else {
			outtextxy(WINDOW_WIDTH / 2 - 100, WINDOW_HEIGHT / 2 + 170, "最终平局！");
		}
	} else if (soldier1.is_dead && !soldier2.is_dead) {
		outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 120, "玩家2胜利！玩家1已阵亡");
	} else if (soldier2.is_dead && !soldier1.is_dead) {
		outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 120, "玩家1胜利！玩家2已阵亡");
	} else {
		// 两名玩家都活着，比较得分
		if (player1_score > player2_score) {
			outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 120, "玩家1胜利！得分更高");
		} else if (player2_score > player1_score) {
			outtextxy(WINDOW_WIDTH / 2 - 200, WINDOW_HEIGHT / 2 + 120, "玩家2胜利！得分更高");
		} else {
			outtextxy(WINDOW_WIDTH / 2 - 100, WINDOW_HEIGHT / 2 + 120, "平局！");
		}
	}

	// 显示按任意键返回主菜单
	settextcolor(WHITE);
	settextstyle(25, 0, _T("宋体"));
	// 如果两名玩家都死亡，将文本位置下移
	if (soldier1.is_dead && soldier2.is_dead) {
		outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 + 220, "按任意键返回主菜单...");
	} else {
		outtextxy(WINDOW_WIDTH / 2 - 150, WINDOW_HEIGHT / 2 + 180, "按任意键返回主菜单...");
	}
	FlushBatchDraw();

	// 释放内存
	free_bombing_areas();

	// 释放敌人子弹内存
	EnemyBullet* current_enemy_bullet = enemy_bullet_head;
	while (current_enemy_bullet != NULL) {
		EnemyBullet* temp = current_enemy_bullet;
		current_enemy_bullet = current_enemy_bullet->next;
		free(temp);
	}
	enemy_bullet_head = NULL;

	// 释放玩家2炸弹内存
	Bomb* current_bomb = bomb_head;
	while (current_bomb != NULL) {
		Bomb* temp = current_bomb;
		current_bomb = current_bomb->next;
		free(temp);
	}
	bomb_head = NULL;

	// 使用EasyX消息机制等待用户按键
	bool key_pressed = false;
	ExMessage msg;
	while (!key_pressed) {
		// 检查是否有按键消息
		if (peekmessage(&msg, EX_KEY | EX_MOUSE)) {
			if (msg.message == WM_KEYDOWN || msg.message == WM_LBUTTONDOWN) {
				key_pressed = true;
			}
		}
		Sleep(10); // 短暂休眠，减少CPU占用
	}

	// 重置游戏状态
	game_over = 0;

	// 使用安全的内存清理函数
	cleanup_all_memory();

	// 清空障碍物
	Obstacle* current_obstacle = obstacle_head;
	while (current_obstacle != NULL) {
		Obstacle* temp = current_obstacle;
		current_obstacle = current_obstacle->next;
		free(temp);
	}
	obstacle_head = NULL;
	obstacle_count = 0;

	// 重置玩家状态
	soldier1.x = MAP_WIDTH / 2 - BLOCK_SIZE * 5;
	soldier1.y = MAP_HEIGHT / 2;
	soldier1.site = 2;
	soldier1.health = 3;
	soldier1.invincible = 0;
	soldier1.last_hit_time = 0;
	soldier1.is_dead = false;
	soldier1.being_rescued = false;
	soldier1.death_time = 0;
	soldier1.being_healed = false;
	soldier1.heal_start_time = 0;
	soldier1.heal_progress = 0.0f;

	soldier2.x = MAP_WIDTH / 2 + BLOCK_SIZE * 5;
	soldier2.y = MAP_HEIGHT / 2;
	soldier2.site = 4;
	soldier2.health = 3;
	soldier2.invincible = 0;
	soldier2.last_hit_time = 0;
	soldier2.is_dead = false;
	soldier2.being_rescued = false;
	soldier2.death_time = 0;
	soldier2.being_healed = false;
	soldier2.heal_start_time = 0;
	soldier2.heal_progress = 0.0f;

	// 重置得分和等级
	player1_score = 0;
	player2_score = 0;
	player1_kills = 0;
	player2_kills = 0;
	player1_level = 1;
	player2_level = 1;

	// 重置狂暴模式
	rage_mode_active = false;
	rage_mode_count = 0;
	last_rage_mode_time = 0;
}

// 在游戏结束时释放轰炸区域内存
void free_bombing_areas() {
	BombingArea* current = bombing_area_head;
	while (current != NULL) {
		// 释放轰炸区域中的所有格子
		BombingCell* cell = current->cells;
		while (cell != NULL) {
			BombingCell* temp_cell = cell;
			cell = cell->next;
			free(temp_cell);
		}

		// 释放轰炸区域本身
		BombingArea* temp = current;
		current = current->next;
		free(temp);
	}
	bombing_area_head = NULL;
}

// 实现 check_bombing_areas 函数
void check_bombing_areas() {
	DWORD current_time = GetTickCount();
	BombingArea* current = bombing_area_head;
	BombingArea* prev = NULL;

	while (current != NULL) {
		// 检查轰炸区域是否激活
		if (current->active) {
			// 如果未标记，先标记为警告状态（将在绘制时显示为黄色）
			if (!current->marked) {
				current->marked = 1;
				current->mark_time = current_time;
			}
			// 如果已标记，检查是否到达轰炸时间（2秒后）
			else if (current_time - current->mark_time >= 2000) {
				// 执行轰炸判定

				// 检查轰炸区域内的敌人
				Enemy* current_enemy = enemy_head;
				Enemy* prev_enemy = NULL;
				while (current_enemy != NULL) {
					// 检查敌人是否在3x3轰炸区域内
					bool in_bombing_area = false;

					// 检查敌人是否在轰炸区域内的任何格子中
					BombingCell* cell = current->cells;
					while (cell != NULL && !in_bombing_area) {
						if (current_enemy->x + BLOCK_SIZE > cell->x &&
							current_enemy->x < cell->x + BLOCK_SIZE &&
							current_enemy->y + BLOCK_SIZE > cell->y &&
							current_enemy->y < cell->y + BLOCK_SIZE) {
							in_bombing_area = true;
						}
						cell = cell->next;
					}

					if (in_bombing_area) {
						// 移除敌人
						if (prev_enemy == NULL) {
							enemy_head = current_enemy->next;
						} else {
							prev_enemy->next = current_enemy->next;
						}
						Enemy* temp = current_enemy;
						current_enemy = current_enemy->next;
						free(temp);
						enemy_count--; // 减少敌人计数

						// 增加两个玩家的得分（轰炸区消灭敌人得25分，平分50分）
						player1_score += 25;
						player2_score += 25;
						printf("敌人在轰炸中被消灭！两位玩家各得25分，玩家1得分：%d，玩家2得分：%d，剩余敌人：%d/%d\n",
							player1_score, player2_score, enemy_count, MAX_ENEMIES);
						continue;
					}
					prev_enemy = current_enemy;
					current_enemy = current_enemy->next;
				}

				// 检查轰炸区域内的玩家1（只有在玩家1未死亡时才检测）
				// 检查玩家1是否在轰炸区域内的任何格子中
				bool player1_in_area = false;
				if (!soldier1.is_dead) {
					BombingCell* cell = current->cells;
					while (cell != NULL && !player1_in_area) {
						if (soldier1.x + BLOCK_SIZE > cell->x &&
							soldier1.x < cell->x + BLOCK_SIZE &&
							soldier1.y + BLOCK_SIZE > cell->y &&
							soldier1.y < cell->y + BLOCK_SIZE) {
							player1_in_area = true;
						}
						cell = cell->next;
					}
				}

				if (player1_in_area) {

					// 检查玩家1是否处于无敌状态
					DWORD current_time = GetTickCount();
					if (current_time - soldier1.last_hit_time >= soldier1.invincible) {
						// 玩家1受伤，减少生命值
						soldier1.health--;
						printf("玩家1在轰炸中受伤！失去1条生命！剩余生命：%d\n", soldier1.health);

						// 设置无敌时间（2秒）
						soldier1.invincible = 2000;
						soldier1.last_hit_time = current_time;

						// 如果生命值为0，玩家死亡但游戏不结束
						if (soldier1.health <= 0 && !soldier1.is_dead) {
							printf("玩家1在轰炸中死亡，等待救援！\n");
							soldier1.is_dead = true;
							soldier1.death_time = current_time;

							// 只有当两个玩家都死亡时，游戏才结束
							if (soldier1.is_dead && soldier2.is_dead) {
								printf("两名玩家均已阵亡，游戏结束！\n");
								game_over = 1;
							}
						}

						// 扣分
						if (player1_score >= 200) {
							player1_score -= 200;
							printf("玩家1失去200分！当前得分：%d\n", player1_score);
						}
					}
				}

				// 检查轰炸区域内的玩家2（只有在玩家2未死亡时才检测）
				// 检查玩家2是否在轰炸区域内的任何格子中
				bool player2_in_area = false;
				if (!soldier2.is_dead) {
					BombingCell* cell = current->cells;
					while (cell != NULL && !player2_in_area) {
						if (soldier2.x + BLOCK_SIZE > cell->x &&
							soldier2.x < cell->x + BLOCK_SIZE &&
							soldier2.y + BLOCK_SIZE > cell->y &&
							soldier2.y < cell->y + BLOCK_SIZE) {
							player2_in_area = true;
						}
						cell = cell->next;
					}
				}

				if (player2_in_area) {

					// 检查玩家2是否处于无敌状态
					DWORD current_time = GetTickCount();
					if (current_time - soldier2.last_hit_time >= soldier2.invincible) {
						// 玩家2受伤，减少生命值
						soldier2.health--;
						printf("玩家2在轰炸中受伤！失去1条生命！剩余生命：%d\n", soldier2.health);

						// 设置无敌时间（2秒）
						soldier2.invincible = 2000;
						soldier2.last_hit_time = current_time;

						// 如果生命值为0，玩家死亡但游戏不结束
						if (soldier2.health <= 0 && !soldier2.is_dead) {
							printf("玩家2在轰炸中死亡，等待救援！\n");
							soldier2.is_dead = true;
							soldier2.death_time = current_time;

							// 只有当两个玩家都死亡时，游戏才结束
							if (soldier1.is_dead && soldier2.is_dead) {
								printf("两名玩家均已阵亡，游戏结束！\n");
								game_over = 1;
							}
						}

						// 扣分
						if (player2_score >= 200) {
							player2_score -= 200;
							printf("玩家2失去200分！当前得分：%d\n", player2_score);
						}
					}
				}

				// 检查轰炸区域内的障碍物
				Obstacle* current_obstacle = obstacle_head;
				Obstacle* prev_obstacle = NULL;
				while (current_obstacle != NULL) {
					// 检查障碍物是否在轰炸区域内的任何格子中
					bool obstacle_in_area = false;
					BombingCell* cell = current->cells;
					while (cell != NULL && !obstacle_in_area) {
						if (current_obstacle->x + BLOCK_SIZE > cell->x &&
							current_obstacle->x < cell->x + BLOCK_SIZE &&
							current_obstacle->y + BLOCK_SIZE > cell->y &&
							current_obstacle->y < cell->y + BLOCK_SIZE) {
							obstacle_in_area = true;
						}
						cell = cell->next;
					}

					if (obstacle_in_area) {
						// 只有岩石障碍物会被炸弹炸毁，水域障碍物不受影响
						if (current_obstacle->type == OBSTACLE_ROCK) {
							// 移除障碍物
							if (prev_obstacle == NULL) {
								obstacle_head = current_obstacle->next;
							} else {
								prev_obstacle->next = current_obstacle->next;
							}
							Obstacle* temp = current_obstacle;
							current_obstacle = current_obstacle->next;
							free(temp);
							obstacle_count--; // 减少障碍物计数
							printf("岩石障碍物在轰炸中被清除！当前障碍物数量：%d/%d\n",
								obstacle_count, MAX_OBSTACLES);
							continue;
						} else {
							// 水域障碍物不受影响
							printf("水域障碍物不受炸弹影响！\n");
							prev_obstacle = current_obstacle;
							current_obstacle = current_obstacle->next;
							continue;
						}
					}
					prev_obstacle = current_obstacle;
					current_obstacle = current_obstacle->next;
				}

				// 轰炸完成后，将区域变为紫色（保持一段时间）
				current->active = 0; // 不再激活轰炸判定
			}
		}

		// 检查紫色区域是否需要清除（轰炸后5秒）
		if (!current->active && current->marked &&
			current_time - current->mark_time >= 5000) {
			// 移除轰炸区域
			if (prev == NULL) {
				bombing_area_head = current->next;
				free(current);
				current = bombing_area_head;
			} else {
				prev->next = current->next;
				free(current);
				current = prev->next;
			}
		} else {
			prev = current;
			current = current->next;
		}
	}
}

// 实现 spawn_random_bombing_areas 函数
void spawn_random_bombing_areas(int count) {
	// 计算当前轰炸区数量
	int current_count = 0;
	BombingArea* current = bombing_area_head;
	while (current != NULL) {
		current_count++;
		current = current->next;
	}

	// 限制最大轰炸区域数量为10个
	int max_areas = 10;
	if (current_count >= max_areas) {
		return;  // 如果已经达到最大数量，不再生成
	}

	// 计算实际需要生成的数量
	int to_spawn = (current_count + count > max_areas) ? (max_areas - current_count) : count;

	// 生成新的轰炸区域
	for (int i = 0; i < to_spawn; i++) {
		spawn_random_bombing_area();
	}
}

// 添加显示游戏背景故事的函数
void show_story() {
	// 设置黑色背景
	setbkcolor(BLACK);
	cleardevice();

	// 设置文字样式
	settextcolor(WHITE);
	settextstyle(24, 0, _T("宋体"));

	// 背景故事文本
	const char* story_lines[] = {
		"无尽模式",
		"",
		"你作为敌后抗日战场的游击队",
		"你要尽可能地利用手头的一切资源消灭敌人",
		"直至流尽最后一滴血。",
		"",
		"敌人会从四面八方不断涌来，",
		"而你只有你的队友，",
		"消灭敌人，然后从他们身上拾取补给。",
		"",
		"同志，抗战终会胜利！",
		"",
		"按任意键开始游戏...",
		"按Enter键跳过文字动画"
	};

	// 逐行显示文本
	const int line_count = sizeof(story_lines) / sizeof(story_lines[0]);
	const int start_y = 100;
	const int line_height = 30;

	// 标记是否需要跳过动画
	bool skip_animation = false;

	// 预先清空所有行的位置
	for (int i = 0; i < line_count; i++) {
		int y = start_y + i * line_height;
		setfillcolor(BLACK);
		solidrectangle(0, y, WINDOW_WIDTH, y + line_height);
	}

	for (int i = 0; i < line_count; i++) {
		// 计算当前行的Y坐标
		int y = start_y + i * line_height;

		// 获取当前行文本
		const char* line = story_lines[i];
		int len = strlen(line);
		char buffer[256] = {0};

		// 如果需要跳过动画，直接显示完整文本
		if (skip_animation) {
			outtextxy(WINDOW_WIDTH / 2 - textwidth(line) / 2, y, line);
			FlushBatchDraw();
			continue;
		}

		// 逐字显示当前行文本
		for (int j = 0; j < len; j++) {
			// 检查是否按下Enter键
			if (GetAsyncKeyState(VK_RETURN) & 0x8000) {
				skip_animation = true;
				break; // 跳出逐字显示循环
			}

			// 复制部分字符串
			strncpy(buffer, line, j + 1);
			buffer[j + 1] = '\0';

			// 清除当前行
			setfillcolor(BLACK);
			solidrectangle(0, y, WINDOW_WIDTH, y + line_height);

			// 显示文本
			outtextxy(WINDOW_WIDTH / 2 - textwidth(line) / 2, y, buffer);

			// 延时，控制显示速度
			Sleep(30);
			FlushBatchDraw();
		}

		// 如果需要跳过动画，显示所有剩余文本并跳出循环
		if (skip_animation) {
			// 显示当前行的完整文本（以防被中断）
			setfillcolor(BLACK);
			solidrectangle(0, y, WINDOW_WIDTH, y + line_height);
			outtextxy(WINDOW_WIDTH / 2 - textwidth(line) / 2, y, line);

			// 显示所有剩余行
			for (int k = i + 1; k < line_count; k++) {
				int next_y = start_y + k * line_height;
				const char* next_line = story_lines[k];
				outtextxy(WINDOW_WIDTH / 2 - textwidth(next_line) / 2, next_y, next_line);
			}

			FlushBatchDraw();
			break; // 跳出逐行显示循环
		}

		// 每行显示完后稍作停顿
		Sleep(200);
	}

	// 使用EasyX消息机制等待用户按键
	bool key_pressed = false;
	ExMessage msg;
	while (!key_pressed) {
		// 检查是否有按键消息
		if (peekmessage(&msg, EX_KEY | EX_MOUSE)) {
			if (msg.message == WM_KEYDOWN || msg.message == WM_LBUTTONDOWN) {
				key_pressed = true;
			}
		}
		Sleep(10); // 短暂休眠，减少CPU占用
	}

	// 清屏，准备开始游戏
	cleardevice();
}

// 更新摄像机位置函数 - 跟踪两个玩家的中心点
void update_camera() {
	// 计算两个玩家的中心点
	int center_x = (soldier1.x + soldier2.x) / 2;
	int center_y = (soldier1.y + soldier2.y) / 2;

	// 计算理想的摄像机位置（使两个玩家的中心点位于屏幕中央）
	int target_x = center_x - WINDOW_WIDTH / 2 + BLOCK_SIZE / 2;
	int target_y = center_y - WINDOW_HEIGHT / 2 + BLOCK_SIZE / 2;

	// 平滑过渡（摄像机追踪）
	camera1.x = target_x;
	camera1.y = target_y;

	// 确保摄像机不会超出地图边界
	if (camera1.x < 0) camera1.x = 0;
	if (camera1.y < 0) camera1.y = 0;
	if (camera1.x > MAP_WIDTH - WINDOW_WIDTH) camera1.x = MAP_WIDTH - WINDOW_WIDTH;
	if (camera1.y > MAP_HEIGHT - WINDOW_HEIGHT) camera1.y = MAP_HEIGHT - WINDOW_HEIGHT;

	// 两个玩家使用同一个摄像机
	camera2.x = camera1.x;
	camera2.y = camera1.y;
}

// 为了兼容性保留这些函数，但它们现在只是调用主摄像机更新函数
void update_camera1() {
	update_camera();
}

void update_camera2() {
	// 不需要额外操作，已经在update_camera中更新了camera2
}

// 玩家1世界坐标转屏幕坐标 - 现在与主坐标转换函数相同
int world_to_screen_x1(int world_x) {
	return world_to_screen_x(world_x);
}

int world_to_screen_y1(int world_y) {
	return world_to_screen_y(world_y);
}

// 玩家2世界坐标转屏幕坐标 - 现在与主坐标转换函数相同
int world_to_screen_x2(int world_x) {
	return world_to_screen_x(world_x);
}

int world_to_screen_y2(int world_y) {
	return world_to_screen_y(world_y);
}

// 世界坐标转屏幕坐标（使用玩家1的摄像机）
int world_to_screen_x(int world_x) {
	return world_x - camera1.x;
}

int world_to_screen_y(int world_y) {
	return world_y - camera1.y;
}

// 屏幕坐标转世界坐标（使用玩家1的摄像机）
int screen_to_world_x(int screen_x) {
	return screen_x + camera1.x;
}

int screen_to_world_y(int screen_y) {
	return screen_y + camera1.y;
}

// 初始化地图和迷雾
void init_map() {
	// 初始化所有格子为空
	for (int x = 0; x < MAP_GRID_WIDTH; x++) {
		for (int y = 0; y < MAP_GRID_HEIGHT; y++) {
			map_grid[x][y] = GRID_EMPTY;
		}
	}

	// 清空现有的障碍物链表
	Obstacle* current = obstacle_head;
	while (current != NULL) {
		Obstacle* temp = current;
		current = current->next;
		free(temp);
	}
	obstacle_head = NULL;
	obstacle_count = 0;

	// 调试输出
	printf("地图初始化完成，障碍物数量：%d\n", count_obstacles());
}

// 添加一个函数来计算障碍物数量（用于调试）
int count_obstacles() {
	int count = 0;
	Obstacle* current = obstacle_head;
	while (current != NULL) {
		count++;
		current = current->next;
	}
	return count;
}

// 绘制玩家1的小地图
void draw_minimap1() {
	int minimap_size = 200; // 在全屏模式下使用更大的小地图
	int minimap_x = 20; // 放在左上角
	int minimap_y = 40; // 留出空间显示得分
	int border = 2;

	// 绘制小地图边框
	setlinecolor(WHITE);
	setfillcolor(BLACK);
	fillrectangle(minimap_x - border, minimap_y - border,
		minimap_x + minimap_size + border, minimap_y + minimap_size + border);

	// 计算小地图比例
	double scale_x = (double)minimap_size / MAP_WIDTH;
	double scale_y = (double)minimap_size / MAP_HEIGHT;

	// 绘制小地图背景
	setfillcolor(RGB(200, 200, 200)); // 浅灰色
	fillrectangle(minimap_x, minimap_y, minimap_x + minimap_size, minimap_y + minimap_size);

	// 直接绘制障碍物（使用链表）- 根据类型使用不同颜色
	Obstacle* obs_current = obstacle_head;
	while (obs_current != NULL) {
		int mm_x = minimap_x + (int)(obs_current->x * scale_x);
		int mm_y = minimap_y + (int)(obs_current->y * scale_y);
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

		// 根据障碍物类型选择不同的颜色
		if (obs_current->type == OBSTACLE_WATER) {
			// 水域障碍物 - 蓝色
			setfillcolor(RGB(0, 0, 200)); // 蓝色
		} else {
			// 岩石障碍物 - 灰色
			setfillcolor(GRAY);
		}

		fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

		obs_current = obs_current->next;
	}

	// 绘制轰炸区域
	BombingArea* bombing_current = bombing_area_head;
	while (bombing_current != NULL) {
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

				// 根据轰炸区域状态设置不同的颜色
		if (bombing_current->active) {
			if (bombing_current->marked) {
				// 已标记的轰炸区域（即将爆炸）- 使用明亮的红色
				setfillcolor(RGB(255, 50, 50)); // 亮红色

				// 添加闪烁效果
				DWORD current_time = GetTickCount();
				if ((current_time / 100) % 2 == 0) {
					setfillcolor(RGB(255, 0, 0)); // 更亮的红色
				} else {
					setfillcolor(RGB(255, 100, 100)); // 稍微亮一点的红色
				}
			} else {
				// 未标记的轰炸区域（警告阶段）- 使用黄色
				setfillcolor(YELLOW);
			}
		} else {
			// 已轰炸的区域（减速区域）- 使用明显的紫色
			setfillcolor(RGB(180, 0, 180)); // 亮紫色
		}

		// 绘制轰炸区域中的每个格子
		BombingCell* cell = bombing_current->cells;
		while (cell != NULL) {
			int mm_x = minimap_x + (int)(cell->x * scale_x);
			int mm_y = minimap_y + (int)(cell->y * scale_y);

			// 确保在小地图范围内
			if (mm_x >= minimap_x && mm_x < minimap_x + minimap_size &&
				mm_y >= minimap_y && mm_y < minimap_y + minimap_size) {
				// 填充颜色
				fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

				// 为已轰炸的紫色区域添加边框，使其在小地图上更加明显
				if (!bombing_current->active) {
					setlinecolor(WHITE);
					rectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				}
			}

			cell = cell->next;
		}

		bombing_current = bombing_current->next;
	}

	// 绘制敌人
	Enemy* enemy_current = enemy_head;
	while (enemy_current != NULL) {
		int mm_x = minimap_x + (int)(enemy_current->x * scale_x);
		int mm_y = minimap_y + (int)(enemy_current->y * scale_y);
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

		// 根据敌人类型设置不同的颜色
		if (enemy_current->type == ENEMY_SNIPER) {
			// 狙击手敌人 - 橙色
			setfillcolor(RGB(255, 165, 0)); // 橙色
		} else if (enemy_current->type == ENEMY_TANK) {
			// 坦克敌人 - 深红色
			setfillcolor(RGB(139, 0, 0)); // 深红色
		} else {
			// 普通敌人 - 红色
			setfillcolor(RED);
		}

		fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

		enemy_current = enemy_current->next;
	}

	// 绘制玩家2的炸弹
	Bomb* bomb_current = bomb_head;
	while (bomb_current != NULL) {
		if (bomb_current->active) {
			int mm_x = minimap_x + (int)(bomb_current->x * scale_x);
			int mm_y = minimap_y + (int)(bomb_current->y * scale_y);
			int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

			// 在小地图上显示炸弹中心点为黄色
			setfillcolor(RGB(255, 255, 0));
			fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

			// 创建一个临时的候选格子数组，用于预览可能的爆炸区域
			typedef struct {
				int x, y;
			} PreviewCell;

			PreviewCell preview_cells[16]; // 最多16个格子
			int preview_count = 1; // 中心格子已经确定

			// 添加中心格子
			preview_cells[0].x = bomb_current->x;
			preview_cells[0].y = bomb_current->y;

			// 随机生成预览格子（模拟爆炸区域）
			int max_preview = 8 + rand() % 9; // 8到16个格子

			// 候选格子数组
			PreviewCell candidates[4 * 16];
			int candidate_count = 0;

			// 添加中心格子的相邻位置作为候选
			int dx[4] = {0, 1, 0, -1}; // 上、右、下、左
			int dy[4] = {-1, 0, 1, 0};

			for (int i = 0; i < 4; i++) {
				int nx = bomb_current->x + dx[i] * BLOCK_SIZE;
				int ny = bomb_current->y + dy[i] * BLOCK_SIZE;

				// 检查位置是否在地图范围内
				if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
					candidates[candidate_count].x = nx;
					candidates[candidate_count].y = ny;
					candidate_count++;
				}
			}

			// 随机添加更多预览格子
			while (preview_count < max_preview && candidate_count > 0) {
				// 随机选择一个候选格子
				int idx = rand() % candidate_count;
				int next_x = candidates[idx].x;
				int next_y = candidates[idx].y;

				// 检查该格子是否已经在预览中
				bool already_in_preview = false;
				for (int i = 0; i < preview_count; i++) {
					if (preview_cells[i].x == next_x && preview_cells[i].y == next_y) {
						already_in_preview = true;
						break;
					}
				}

				if (!already_in_preview) {
					// 添加到预览格子
					preview_cells[preview_count].x = next_x;
					preview_cells[preview_count].y = next_y;
					preview_count++;

					// 添加新格子的相邻位置作为候选
					for (int i = 0; i < 4; i++) {
						int nx = next_x + dx[i] * BLOCK_SIZE;
						int ny = next_y + dy[i] * BLOCK_SIZE;

						// 检查位置是否在地图范围内
						if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
							// 添加到候选数组
							if (candidate_count < 4 * 16) {
								candidates[candidate_count].x = nx;
								candidates[candidate_count].y = ny;
								candidate_count++;
							}
						}
					}
				}

				// 移除当前候选格子
				candidates[idx] = candidates[candidate_count - 1];
				candidate_count--;
			}

			// 绘制预览格子
			setfillcolor(RGB(255, 100, 100));
			for (int i = 1; i < preview_count; i++) { // 从1开始，跳过中心格子
				int px = minimap_x + (int)(preview_cells[i].x * scale_x);
				int py = minimap_y + (int)(preview_cells[i].y * scale_y);

				// 确保在小地图范围内
				if (px >= minimap_x && px < minimap_x + minimap_size &&
					py >= minimap_y && py < minimap_y + minimap_size) {
					// 绘制格子
					fillrectangle(px, py, px + mm_size, py + mm_size);
				}
			}
		}
		bomb_current = bomb_current->next;
	}

	// 绘制玩家1位置（蓝色点）
	int player1_mm_x = minimap_x + (int)(soldier1.x * scale_x);
	int player1_mm_y = minimap_y + (int)(soldier1.y * scale_y);
	setfillcolor(BLUE);
	fillrectangle(player1_mm_x, player1_mm_y,
		player1_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)),
		player1_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));

	// 绘制玩家2位置（绿色点）
	int player2_mm_x = minimap_x + (int)(soldier2.x * scale_x);
	int player2_mm_y = minimap_y + (int)(soldier2.y * scale_y);
	setfillcolor(GREEN);
	fillrectangle(player2_mm_x, player2_mm_y,
		player2_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)),
		player2_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));

	// 绘制当前视野范围（白色矩形）
	int view_mm_x = minimap_x + (int)(camera1.x * scale_x);
	int view_mm_y = minimap_y + (int)(camera1.y * scale_y);
	int view_mm_width = (int)(WINDOW_WIDTH * scale_x);
	int view_mm_height = (int)(WINDOW_HEIGHT * scale_y);
	setlinecolor(WHITE);
	rectangle(view_mm_x, view_mm_y,
		view_mm_x + view_mm_width, view_mm_y + view_mm_height);

	// 显示玩家1得分
	char score_text[50];
	sprintf(score_text, "P1: %d", player1_score);
	settextcolor(WHITE);
	settextstyle(16, 0, _T("宋体"));
	outtextxy(minimap_x, minimap_y + minimap_size + 5, score_text);

	// 在小地图下方显示玩家1的键位说明
	int key_info_y = minimap_y + minimap_size + 25; // 在得分下方
	int line_height = 18; // 稍微减小行高以节省空间

	// 设置文本样式
	settextstyle(14, 0, _T("宋体")); // 稍微减小字体

	// 显示键位说明
	outtextxy(minimap_x, key_info_y, _T("玩家1控制:"));
	outtextxy(minimap_x, key_info_y + line_height, _T("W/A/S/D - 移动"));
	outtextxy(minimap_x, key_info_y + line_height * 2, _T("C - 射击"));
	outtextxy(minimap_x, key_info_y + line_height * 3, _T("E - 治疗队友(按住)"));
	outtextxy(minimap_x, key_info_y + line_height * 4, _T("H - 复活队友"));

	// 绘制等级和升级加成信息（只调用一次）
	draw_minimap1_level_info(minimap_x, minimap_y, minimap_size);
}

// 绘制玩家2的小地图
void draw_minimap2() {
	int minimap_size = 200; // 在全屏模式下使用更大的小地图
	int minimap_x = WINDOW_WIDTH - minimap_size - 20; // 放在右上角
	int minimap_y = 40; // 留出空间显示得分
	int border = 2;

	// 绘制小地图边框
	setlinecolor(WHITE);
	setfillcolor(BLACK);
	fillrectangle(minimap_x - border, minimap_y - border,
		minimap_x + minimap_size + border, minimap_y + minimap_size + border);

	// 计算小地图比例
	double scale_x = (double)minimap_size / MAP_WIDTH;
	double scale_y = (double)minimap_size / MAP_HEIGHT;

	// 绘制小地图背景
	setfillcolor(RGB(200, 200, 200)); // 浅灰色
	fillrectangle(minimap_x, minimap_y, minimap_x + minimap_size, minimap_y + minimap_size);

	// 直接绘制障碍物（使用链表）- 根据类型使用不同颜色
	Obstacle* obs_current = obstacle_head;
	while (obs_current != NULL) {
		int mm_x = minimap_x + (int)(obs_current->x * scale_x);
		int mm_y = minimap_y + (int)(obs_current->y * scale_y);
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

		// 根据障碍物类型选择不同的颜色
		if (obs_current->type == OBSTACLE_WATER) {
			// 水域障碍物 - 蓝色
			setfillcolor(RGB(0, 0, 200)); // 蓝色
		} else {
			// 岩石障碍物 - 灰色
			setfillcolor(GRAY);
		}

		fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

		obs_current = obs_current->next;
	}

	// 绘制轰炸区域
	BombingArea* bombing_current = bombing_area_head;
	while (bombing_current != NULL) {
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

		// 根据轰炸区域状态设置不同的颜色
		if (bombing_current->active) {
			if (bombing_current->marked) {
				// 已标记的轰炸区域（即将爆炸）- 使用明亮的红色
				setfillcolor(RGB(255, 50, 50)); // 亮红色

				// 添加闪烁效果
				DWORD current_time = GetTickCount();
				if ((current_time / 100) % 2 == 0) {
					setfillcolor(RGB(255, 0, 0)); // 更亮的红色
				} else {
					setfillcolor(RGB(255, 100, 100)); // 稍微亮一点的红色
				}
			} else {
				// 未标记的轰炸区域（警告阶段）- 使用黄色
				setfillcolor(YELLOW);
			}
		} else {
			// 已轰炸的区域（减速区域）- 使用明显的紫色
			setfillcolor(RGB(180, 0, 180)); // 亮紫色
		}

		// 绘制轰炸区域中的每个格子
		BombingCell* cell = bombing_current->cells;
		while (cell != NULL) {
			int mm_x = minimap_x + (int)(cell->x * scale_x);
			int mm_y = minimap_y + (int)(cell->y * scale_y);

			// 确保在小地图范围内
			if (mm_x >= minimap_x && mm_x < minimap_x + minimap_size &&
				mm_y >= minimap_y && mm_y < minimap_y + minimap_size) {
				// 填充颜色
				fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

				// 为已轰炸的紫色区域添加边框，使其在小地图上更加明显
				if (!bombing_current->active) {
					setlinecolor(WHITE);
					rectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				}
			}

			cell = cell->next;
		}

		bombing_current = bombing_current->next;
	}

	// 绘制敌人
	Enemy* enemy_current = enemy_head;
	while (enemy_current != NULL) {
		int mm_x = minimap_x + (int)(enemy_current->x * scale_x);
		int mm_y = minimap_y + (int)(enemy_current->y * scale_y);
		int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

		// 根据敌人类型设置不同的颜色
		if (enemy_current->type == ENEMY_SNIPER) {
			// 狙击手敌人 - 橙色
			setfillcolor(RGB(255, 165, 0)); // 橙色
		} else if (enemy_current->type == ENEMY_TANK) {
			// 坦克敌人 - 深红色
			setfillcolor(RGB(139, 0, 0)); // 深红色
		} else {
			// 普通敌人 - 红色
			setfillcolor(RED);
		}

		fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

		enemy_current = enemy_current->next;
	}

	// 绘制玩家2的炸弹
	Bomb* bomb_current = bomb_head;
	while (bomb_current != NULL) {
		if (bomb_current->active) {
			int mm_x = minimap_x + (int)(bomb_current->x * scale_x);
			int mm_y = minimap_y + (int)(bomb_current->y * scale_y);
			int mm_size = std::max(1, (int)(BLOCK_SIZE * scale_x));

			// 在小地图上显示炸弹中心点为黄色
			setfillcolor(RGB(255, 255, 0));
			fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);

			// 创建一个临时的候选格子数组，用于预览可能的爆炸区域
			typedef struct {
				int x, y;
			} PreviewCell;

			PreviewCell preview_cells[16]; // 最多16个格子
			int preview_count = 1; // 中心格子已经确定

			// 添加中心格子
			preview_cells[0].x = bomb_current->x;
			preview_cells[0].y = bomb_current->y;

			// 随机生成预览格子（模拟爆炸区域）
			int max_preview = 8 + rand() % 9; // 8到16个格子

			// 候选格子数组
			PreviewCell candidates[4 * 16];
			int candidate_count = 0;

			// 添加中心格子的相邻位置作为候选
			int dx[4] = {0, 1, 0, -1}; // 上、右、下、左
			int dy[4] = {-1, 0, 1, 0};

			for (int i = 0; i < 4; i++) {
				int nx = bomb_current->x + dx[i] * BLOCK_SIZE;
				int ny = bomb_current->y + dy[i] * BLOCK_SIZE;

				// 检查位置是否在地图范围内
				if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
					candidates[candidate_count].x = nx;
					candidates[candidate_count].y = ny;
					candidate_count++;
				}
			}

			// 随机添加更多预览格子
			while (preview_count < max_preview && candidate_count > 0) {
				// 随机选择一个候选格子
				int idx = rand() % candidate_count;
				int next_x = candidates[idx].x;
				int next_y = candidates[idx].y;

				// 检查该格子是否已经在预览中
				bool already_in_preview = false;
				for (int i = 0; i < preview_count; i++) {
					if (preview_cells[i].x == next_x && preview_cells[i].y == next_y) {
						already_in_preview = true;
						break;
					}
				}

				if (!already_in_preview) {
					// 添加到预览格子
					preview_cells[preview_count].x = next_x;
					preview_cells[preview_count].y = next_y;
					preview_count++;

					// 添加新格子的相邻位置作为候选
					for (int i = 0; i < 4; i++) {
						int nx = next_x + dx[i] * BLOCK_SIZE;
						int ny = next_y + dy[i] * BLOCK_SIZE;

						// 检查位置是否在地图范围内
						if (nx >= 0 && nx < MAP_WIDTH && ny >= 0 && ny < MAP_HEIGHT) {
							// 添加到候选数组
							if (candidate_count < 4 * 16) {
								candidates[candidate_count].x = nx;
								candidates[candidate_count].y = ny;
								candidate_count++;
							}
						}
					}
				}

				// 移除当前候选格子
				candidates[idx] = candidates[candidate_count - 1];
				candidate_count--;
			}

			// 绘制预览格子
			setfillcolor(RGB(255, 100, 100));
			for (int i = 1; i < preview_count; i++) { // 从1开始，跳过中心格子
				int px = minimap_x + (int)(preview_cells[i].x * scale_x);
				int py = minimap_y + (int)(preview_cells[i].y * scale_y);

				// 确保在小地图范围内
				if (px >= minimap_x && px < minimap_x + minimap_size &&
					py >= minimap_y && py < minimap_y + minimap_size) {
					// 绘制格子
					fillrectangle(px, py, px + mm_size, py + mm_size);
				}
			}
		}
		bomb_current = bomb_current->next;
	}

	// 绘制玩家1位置（蓝色点）
	int player1_mm_x = minimap_x + (int)(soldier1.x * scale_x);
	int player1_mm_y = minimap_y + (int)(soldier1.y * scale_y);
	setfillcolor(BLUE);
	fillrectangle(player1_mm_x, player1_mm_y,
		player1_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)),
		player1_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));

	// 绘制玩家2位置（绿色点）
	int player2_mm_x = minimap_x + (int)(soldier2.x * scale_x);
	int player2_mm_y = minimap_y + (int)(soldier2.y * scale_y);
	setfillcolor(GREEN);
	fillrectangle(player2_mm_x, player2_mm_y,
		player2_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)),
		player2_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));

	// 绘制当前视野范围（白色矩形）
	int view_mm_x = minimap_x + (int)(camera2.x * scale_x);
	int view_mm_y = minimap_y + (int)(camera2.y * scale_y);
	int view_mm_width = (int)(WINDOW_WIDTH * scale_x);
	int view_mm_height = (int)(WINDOW_HEIGHT * scale_y);
	setlinecolor(WHITE);
	rectangle(view_mm_x, view_mm_y,
		view_mm_x + view_mm_width, view_mm_y + view_mm_height);

	// 显示玩家2得分
	char score_text[50];
	sprintf(score_text, "P2: %d", player2_score);
	settextcolor(WHITE);
	settextstyle(16, 0, _T("宋体"));
	outtextxy(minimap_x, minimap_y + minimap_size + 5, score_text);

	// 在小地图下方显示玩家2的键位说明
	int key_info_y = minimap_y + minimap_size + 25; // 在得分下方
	int line_height = 18; // 稍微减小行高以节省空间

	// 设置文本样式
	settextstyle(14, 0, _T("宋体")); // 稍微减小字体

	// 显示键位说明
	outtextxy(minimap_x, key_info_y, _T("玩家2控制:"));
	outtextxy(minimap_x, key_info_y + line_height, _T("↑/↓/←/→ - 移动"));
	outtextxy(minimap_x, key_info_y + line_height * 2, _T("M - 射击/放置炸弹"));
	outtextxy(minimap_x, key_info_y + line_height * 3, _T("L - 治疗队友(按住)"));
	outtextxy(minimap_x, key_info_y + line_height * 4, _T("H - 复活队友"));

	// 绘制等级和升级加成信息（只调用一次）
	draw_minimap2_level_info(minimap_x, minimap_y, minimap_size);
}

// 添加障碍物碰撞检测函数
int obstacle_collision(int x, int y) {
	// 检查是否与任何障碍物碰撞
	Obstacle* current = obstacle_head;
	while (current != NULL) {
		// 所有类型的障碍物（岩石和水域）都会阻挡玩家和敌人的移动
		// 检查矩形碰撞
		if (x < current->x + BLOCK_SIZE &&
			x + BLOCK_SIZE > current->x &&
			y < current->y + BLOCK_SIZE &&
			y + BLOCK_SIZE > current->y) {
			return 1; // 发生碰撞
		}
		current = current->next;
	}
	return 0; // 没有碰撞
}

// 检查是否在紫色轰炸区域内（已轰炸区域）
bool is_in_purple_area(int x, int y) {
	BombingArea* current = bombing_area_head;
	while (current != NULL) {
		// 只检查非激活（已轰炸）的区域
		if (!current->active && current->marked) {
			// 检查是否在任何格子内
			BombingCell* cell = current->cells;
			while (cell != NULL) {
				if (x + BLOCK_SIZE > cell->x &&
					x < cell->x + BLOCK_SIZE &&
					y + BLOCK_SIZE > cell->y &&
					y < cell->y + BLOCK_SIZE) {
					return true;
				}
				cell = cell->next;
			}
		}
		current = current->next;
	}
	return false;
}

// 检查位置是否在玩家视野范围内
bool is_in_player_view(int x, int y) {
	bool in_view1 = false;
	bool in_view2 = false;

	// 只检查存活玩家的视野
	if (!soldier1.is_dead) {
		// 计算玩家1视野范围
		int view_left1 = camera1.x;
		int view_right1 = camera1.x + WINDOW_WIDTH / 2;
		int view_top1 = camera1.y;
		int view_bottom1 = camera1.y + WINDOW_HEIGHT;

		// 检查位置是否在玩家1的视野范围内
		in_view1 = (x >= view_left1 - BLOCK_SIZE && x <= view_right1 + BLOCK_SIZE &&
					y >= view_top1 - BLOCK_SIZE && y <= view_bottom1 + BLOCK_SIZE);
	}

	if (!soldier2.is_dead) {
		// 计算玩家2视野范围
		int view_left2 = camera2.x;
		int view_right2 = camera2.x + WINDOW_WIDTH / 2;
		int view_top2 = camera2.y;
		int view_bottom2 = camera2.y + WINDOW_HEIGHT;

		// 检查位置是否在玩家2的视野范围内
		in_view2 = (x >= view_left2 - BLOCK_SIZE && x <= view_right2 + BLOCK_SIZE &&
					y >= view_top2 - BLOCK_SIZE && y <= view_bottom2 + BLOCK_SIZE);
	}

	return in_view1 || in_view2;
}

// 在玩家视野外随机生成障碍物
void spawn_offscreen_obstacles(int count) {
	// 检查是否已达到最大障碍物数量
	if (obstacle_count >= MAX_OBSTACLES) {
		return;
	}

	// 增加生成数量
	count = (int)(count * 1.5); // 增加50%的障碍物数量

	// 限制实际生成的数量，不超过最大值
	int actual_count = std::min(count, MAX_OBSTACLES - obstacle_count);

	// 有20%的概率生成一组水域而不是单独的障碍物
	if (rand() % 100 < 20 && obstacle_count + 8 <= MAX_OBSTACLES) {
		// 尝试生成一组水域障碍物
		// 随机生成位置，直到找到一个在视野外的位置
		int x, y;
		bool valid_position = false;
		int attempts = 0;

		while (!valid_position && attempts < 20) { // 最多尝试20次
			attempts++;

			// 随机生成位置
			x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
			y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;

			// 检查是否在玩家视野外
			if (!is_in_player_view(x, y)) {
				// 检查是否与现有障碍物重叠
				bool has_obstacle = false;
				Obstacle* current = obstacle_head;
				while (current != NULL) {
					if (current->x == x && current->y == y) {
						has_obstacle = true;
						break;
					}
					current = current->next;
				}

				// 如果没有重叠，则位置有效
				if (!has_obstacle) {
					valid_position = true;
				}
			}
		}

		// 如果找到有效位置，创建水域组
		if (valid_position) {
			// 创建第一个水域障碍物
			Obstacle* first_water = (Obstacle*)malloc(sizeof(Obstacle));
			if (first_water != NULL) {
				first_water->x = x;
				first_water->y = y;
				first_water->type = OBSTACLE_WATER;
				first_water->next = obstacle_head;
				obstacle_head = first_water;
				obstacle_count++;

				// 已生成的水域数量（包括第一个）
				int water_count = 1;

				// 用于BFS的队列
				typedef struct {
					int x, y;
				} Position;

				Position queue[100]; // 假设最多100个位置
				int front = 0, rear = 0;

				// 将第一个水域的位置加入队列
				queue[rear].x = first_water->x;
				queue[rear].y = first_water->y;
				rear++;

				// 方向数组：上、右、下、左
				int dx[] = {0, BLOCK_SIZE, 0, -BLOCK_SIZE};
				int dy[] = {-BLOCK_SIZE, 0, BLOCK_SIZE, 0};

				// 使用BFS算法生成连续的水域
				while (front < rear && water_count < 8 && obstacle_count < MAX_OBSTACLES) {
					// 从队列中取出一个位置
					Position current = queue[front++];

					// 随机打乱方向顺序，使水域形状更自然
					int directions[4] = {0, 1, 2, 3};
					for (int i = 0; i < 4; i++) {
						int j = rand() % 4;
						int temp = directions[i];
						directions[i] = directions[j];
						directions[j] = temp;
					}

					// 尝试四个方向
					for (int i = 0; i < 4 && water_count < 8; i++) {
						int dir = directions[i];
						int new_x = current.x + dx[dir];
						int new_y = current.y + dy[dir];

						// 检查新位置是否在地图范围内且在视野外
						if (new_x >= 0 && new_x < MAP_WIDTH && new_y >= 0 && new_y < MAP_HEIGHT &&
							!is_in_player_view(new_x, new_y)) {

							// 检查新位置是否已有障碍物
							bool has_obstacle = false;
							Obstacle* current_obs = obstacle_head;
							while (current_obs != NULL) {
								if (current_obs->x == new_x && current_obs->y == new_y) {
									has_obstacle = true;
									break;
								}
								current_obs = current_obs->next;
							}

							// 如果没有障碍物，则创建一个新的水域
							if (!has_obstacle) {
								Obstacle* new_water = (Obstacle*)malloc(sizeof(Obstacle));
								if (new_water != NULL) {
									new_water->x = new_x;
									new_water->y = new_y;
									new_water->type = OBSTACLE_WATER;
									new_water->next = obstacle_head;
									obstacle_head = new_water;
									obstacle_count++;
									water_count++;

									// 将新水域的位置加入队列，以便继续扩展
									queue[rear].x = new_x;
									queue[rear].y = new_y;
									rear++;
								}
							}
						}
					}
				}

				printf("在视野外生成了一组连续的水域障碍物，数量：%d\n", water_count);
				return; // 生成水域组后直接返回，不再生成其他障碍物
			}
		}
	}

	// 如果没有生成水域组或者生成失败，则生成单独的障碍物
	for (int i = 0; i < actual_count; i++) {
		// 随机生成位置，直到找到一个在视野外的位置
		int x, y;
		bool valid_position = false;
		int attempts = 0;

		while (!valid_position && attempts < 20) { // 最多尝试20次
			attempts++;

			// 随机生成位置
			x = (rand() % (MAP_WIDTH / BLOCK_SIZE)) * BLOCK_SIZE;
			y = (rand() % (MAP_HEIGHT / BLOCK_SIZE)) * BLOCK_SIZE;

			// 检查是否在玩家视野外
			if (!is_in_player_view(x, y)) {
				// 检查是否与现有障碍物重叠
				bool has_obstacle = false;
				Obstacle* current = obstacle_head;
				while (current != NULL) {
					if (current->x == x && current->y == y) {
						has_obstacle = true;
						break;
					}
					current = current->next;
				}

				// 如果没有重叠，则位置有效
				if (!has_obstacle) {
					valid_position = true;
				}
			}
		}

		// 如果找到有效位置，创建新障碍物
		if (valid_position) {
			Obstacle* new_obstacle = (Obstacle*)malloc(sizeof(Obstacle));
			if (new_obstacle != NULL) {
				new_obstacle->x = x;
				new_obstacle->y = y;

				// 增加单独水域障碍物的生成概率（15%概率，原来是5%）
				new_obstacle->type = (rand() % 100 < 15) ? OBSTACLE_WATER : OBSTACLE_ROCK;

				new_obstacle->next = obstacle_head;
				obstacle_head = new_obstacle;
				obstacle_count++;

				// 增加连接障碍物的生成概率（45%，原来是30%）
				if (rand() % 100 < 45) {
					// 随机决定生成1-3个额外的连接障碍物（原来是1-2个）
					int extra_count = rand() % 3 + 1;
					for (int j = 0; j < extra_count; j++) {
						// 随机选择方向：上、下、左、右
						int direction = rand() % 4;
						int offset_x = 0, offset_y = 0;

						switch (direction) {
							case 0: offset_y = -BLOCK_SIZE; break; // 上
							case 1: offset_x = BLOCK_SIZE; break;  // 右
							case 2: offset_y = BLOCK_SIZE; break;  // 下
							case 3: offset_x = -BLOCK_SIZE; break; // 左
						}

						// 计算新障碍物位置
						int new_x = x + offset_x;
						int new_y = y + offset_y;

						// 检查新位置是否在地图范围内且在视野外
						if (new_x >= 0 && new_x < MAP_WIDTH && new_y >= 0 && new_y < MAP_HEIGHT &&
							!is_in_player_view(new_x, new_y)) {

							// 检查新位置是否已有障碍物
							bool has_obstacle = false;
							Obstacle* current = obstacle_head;
							while (current != NULL) {
								if (current->x == new_x && current->y == new_y) {
									has_obstacle = true;
									break;
								}
								current = current->next;
							}

							// 如果没有障碍物，则创建一个新的
							if (!has_obstacle && obstacle_count < MAX_OBSTACLES) {
								Obstacle* extra_obstacle = (Obstacle*)malloc(sizeof(Obstacle));
								if (extra_obstacle != NULL) {
									extra_obstacle->x = new_x;
									extra_obstacle->y = new_y;
									// 连接的障碍物通常与原障碍物类型相同
									extra_obstacle->type = new_obstacle->type;
									extra_obstacle->next = obstacle_head;
									obstacle_head = extra_obstacle;
									obstacle_count++;
								}
							}
						}
					}
				}
			}
		}
	}
}

// 处理输入
void handle_input() {
	// 获取键盘状态
	// 玩家1 - WASD移动，C射击
	// 玩家2 - 方向键移动，M射击
	// ESC - 退出游戏
	// S - 保存游戏

	// 检查ESC键是否按下
	if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
		game_over = 1; // 设置游戏结束标志
		printf("玩家按下ESC键，游戏结束！\n");
		return;
	}

	// 检查R键是否按下（保存游戏）
	static DWORD last_save_time = 0;
	DWORD current_time = GetTickCount();
	if ((GetAsyncKeyState('R') & 0x8000) && current_time - last_save_time > 2000) { // 防止连续保存，至少间隔2秒
		save_game();
		last_save_time = current_time;
	}

	// 获取升级加成
	Player1Upgrade upgrade1 = get_player1_upgrade();
	Player2Upgrade upgrade2 = get_player2_upgrade();

	// 检查玩家1是否在紫色区域内，如果是则降低移速
	float speed_multiplier1 = upgrade1.movement_speed_multiplier; // 应用升级加成
	if (is_in_purple_area(soldier1.x, soldier1.y)) {
		speed_multiplier1 *= 0.25f; // 在紫色区域内移速降低为当前的25%（四分之一）
		printf("玩家1在紫色区域内，移速大幅降低！\n");
	}

	// 检查玩家2是否在紫色区域内，如果是则降低移速
	float speed_multiplier2 = upgrade2.movement_speed_multiplier; // 应用升级加成
	if (is_in_purple_area(soldier2.x, soldier2.y)) {
		speed_multiplier2 *= 0.25f; // 在紫色区域内移速降低为当前的25%（四分之一）
		printf("玩家2在紫色区域内，移速大幅降低！\n");
	}

	// 玩家1 - WASD移动（如果没有死亡且不在被治疗中）
	if (!soldier1.is_dead && !soldier1.being_healed) {
		bool player_moved = false; // 用于跟踪玩家是否移动

		if (GetAsyncKeyState('W') & 0x8000) {
			// 朝上移动
			int new_x = soldier1.x;
			int new_y = soldier1.y - SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier1;
			soldier1_direction = 0;  // 更新朝向为上
			soldier1.site = 1;       // 更新朝向标记为上

			// 边界检查和障碍物碰撞检测
			if (new_y >= 0) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier1.y = new_y;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState('S') & 0x8000) {
			// 朝下移动
			int new_x = soldier1.x;
			int new_y = soldier1.y + SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier1;
			soldier1_direction = 2;  // 更新朝向为下
			soldier1.site = 3;       // 更新朝向标记为下

			// 边界检查和障碍物碰撞检测
			if (new_y <= MAP_HEIGHT - BLOCK_SIZE) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier1.y = new_y;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState('A') & 0x8000) {
			// 朝左移动
			int new_x = soldier1.x - SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier1;
			int new_y = soldier1.y;
			soldier1_direction = 3;  // 更新朝向为左
			soldier1.site = 4;       // 更新朝向标记为左

			// 边界检查和障碍物碰撞检测
			if (new_x >= 0) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier1.x = new_x;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState('D') & 0x8000) {
			// 朝右移动
			int new_x = soldier1.x + SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier1;
			int new_y = soldier1.y;
			soldier1_direction = 1;  // 更新朝向为右
			soldier1.site = 2;       // 更新朝向标记为右

			// 边界检查和障碍物碰撞检测
			if (new_x <= MAP_WIDTH - BLOCK_SIZE) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier1.x = new_x;
					player_moved = true;
				}
			}
		}

		// 如果玩家1移动了，且正在治疗玩家2，则中断治疗
		if (player_moved && soldier2.being_healed) {
			soldier2.being_healed = false;
			soldier2.heal_progress = 0.0f;
			printf("玩家1移动，取消了对玩家2的治疗！\n");
		}

		// 玩家1射击
		static bool c_key_pressed = false;
		if (GetAsyncKeyState('C') & 0x8000) {
			if (!c_key_pressed) {
				fire_bullet1(soldier1.site);
				c_key_pressed = true;

				// 如果玩家1攻击了，且正在治疗玩家2，则中断治疗
				if (soldier2.being_healed) {
					soldier2.being_healed = false;
					soldier2.heal_progress = 0.0f;
					printf("玩家1攻击，取消了对玩家2的治疗！\n");
				}
			}
		} else {
			c_key_pressed = false;
		}

		// 玩家1救助玩家2（如果玩家2死亡）
		static bool h_key_pressed = false;
		if (GetAsyncKeyState('H') & 0x8000) {
			if (!h_key_pressed) {
				// 检查玩家1是否靠近玩家2
				if (soldier2.is_dead &&
					abs(soldier1.x - soldier2.x) <= BLOCK_SIZE * 2 &&
					abs(soldier1.y - soldier2.y) <= BLOCK_SIZE * 2) {

					// 开始救助
					if (!soldier2.being_rescued) {
						soldier2.being_rescued = true;
						printf("玩家1开始救助玩家2！\n");
					} else {
						// 完成救助
						soldier2.is_dead = false;
						soldier2.being_rescued = false;
						soldier2.health = 1; // 复活后只有1点生命值
						printf("玩家2被玩家1救活了！\n");
					}
				}
				h_key_pressed = true;
			}
		} else {
			h_key_pressed = false;
		}

		// 玩家1治疗玩家2（如果玩家2受伤但未死亡）
		if (GetAsyncKeyState('E') & 0x8000) {
			// 检查玩家1是否靠近玩家2
			if (!soldier2.is_dead && soldier2.health < MAX_HEALTH &&
				abs(soldier1.x - soldier2.x) <= BLOCK_SIZE * 2 &&
				abs(soldier1.y - soldier2.y) <= BLOCK_SIZE * 2) {

				DWORD current_time = GetTickCount();

				// 开始治疗
				if (!soldier2.being_healed) {
					soldier2.being_healed = true;
					soldier2.heal_start_time = current_time;
					soldier2.heal_progress = 0.0f;
					printf("玩家1开始治疗玩家2！\n");
				} else {
					// 更新治疗进度
					float heal_time = (float)(current_time - soldier2.heal_start_time) / 4000.0f; // 4秒完成治疗
					soldier2.heal_progress = heal_time > 1.0f ? 1.0f : heal_time;

					// 如果治疗完成
					if (soldier2.heal_progress >= 1.0f) {
						soldier2.health++; // 增加1点生命值
						soldier2.being_healed = false;
						soldier2.heal_progress = 0.0f;
						printf("玩家2被玩家1治疗，恢复1点生命值！当前生命值：%d\n", soldier2.health);
					}
				}
			}
		} else {
			// 如果松开E键，取消治疗
			if (soldier2.being_healed) {
				soldier2.being_healed = false;
				soldier2.heal_progress = 0.0f;
				printf("玩家1取消了对玩家2的治疗！\n");
			}
		}
	}

	// 玩家2 - 方向键移动（如果没有死亡且不在被治疗中）
	if (!soldier2.is_dead && !soldier2.being_healed) {
		bool player_moved = false; // 用于跟踪玩家是否移动

		if (GetAsyncKeyState(VK_UP) & 0x8000) {
			// 朝上移动
			int new_x = soldier2.x;
			int new_y = soldier2.y - SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier2;
			soldier2_direction = 0;  // 更新朝向为上
			soldier2.site = 1;       // 更新朝向标记为上

			// 边界检查和障碍物碰撞检测
			if (new_y >= 0) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier2.y = new_y;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState(VK_DOWN) & 0x8000) {
			// 朝下移动
			int new_x = soldier2.x;
			int new_y = soldier2.y + SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier2;
			soldier2_direction = 2;  // 更新朝向为下
			soldier2.site = 3;       // 更新朝向标记为下

			// 边界检查和障碍物碰撞检测
			if (new_y <= MAP_HEIGHT - BLOCK_SIZE) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier2.y = new_y;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState(VK_LEFT) & 0x8000) {
			// 朝左移动
			int new_x = soldier2.x - SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier2;
			int new_y = soldier2.y;
			soldier2_direction = 3;  // 更新朝向为左
			soldier2.site = 4;       // 更新朝向标记为左

			// 边界检查和障碍物碰撞检测
			if (new_x >= 0) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier2.x = new_x;
					player_moved = true;
				}
			}
		}
		if (GetAsyncKeyState(VK_RIGHT) & 0x8000) {
			// 朝右移动
			int new_x = soldier2.x + SOLDIER_SPEED * BLOCK_SIZE * speed_multiplier2;
			int new_y = soldier2.y;
			soldier2_direction = 1;  // 更新朝向为右
			soldier2.site = 2;       // 更新朝向标记为右

			// 边界检查和障碍物碰撞检测
			if (new_x <= MAP_WIDTH - BLOCK_SIZE) { // 地图边界检查
				// 障碍物碰撞检测
				if (!obstacle_collision(new_x, new_y)) {
					soldier2.x = new_x;
					player_moved = true;
				}
			}
		}

		// 如果玩家2移动了，且正在治疗玩家1，则中断治疗
		if (player_moved && soldier1.being_healed) {
			soldier1.being_healed = false;
			soldier1.heal_progress = 0.0f;
			printf("玩家2移动，取消了对玩家1的治疗！\n");
		}

		// 玩家2放置炸弹
		static bool m_key_pressed = false;
		if (GetAsyncKeyState('M') & 0x8000) {
			if (!m_key_pressed) {
				place_bomb(); // 放置炸弹
				m_key_pressed = true;

				// 如果玩家2攻击了，且正在治疗玩家1，则中断治疗
				if (soldier1.being_healed) {
					soldier1.being_healed = false;
					soldier1.heal_progress = 0.0f;
					printf("玩家2攻击，取消了对玩家1的治疗！\n");
				}
			}
		} else {
			m_key_pressed = false;
		}

		// 玩家2救助玩家1（如果玩家1死亡）
		static bool h_key_pressed2 = false;
		if (GetAsyncKeyState('H') & 0x8000) {
			if (!h_key_pressed2) {
				// 检查玩家2是否靠近玩家1
				if (soldier1.is_dead &&
					abs(soldier2.x - soldier1.x) <= BLOCK_SIZE * 2 &&
					abs(soldier2.y - soldier1.y) <= BLOCK_SIZE * 2) {

					// 开始救助
					if (!soldier1.being_rescued) {
						soldier1.being_rescued = true;
						printf("玩家2开始救助玩家1！\n");
					} else {
						// 完成救助
						soldier1.is_dead = false;
						soldier1.being_rescued = false;
						soldier1.health = 1; // 复活后只有1点生命值
						printf("玩家1被玩家2救活了！\n");
					}
				}
				h_key_pressed2 = true;
			}
		} else {
			h_key_pressed2 = false;
		}

		// 玩家2治疗玩家1（如果玩家1受伤但未死亡）
		if (GetAsyncKeyState('L') & 0x8000) {
			// 检查玩家2是否靠近玩家1
			if (!soldier1.is_dead && soldier1.health < MAX_HEALTH &&
				abs(soldier2.x - soldier1.x) <= BLOCK_SIZE * 2 &&
				abs(soldier2.y - soldier1.y) <= BLOCK_SIZE * 2) {

				DWORD current_time = GetTickCount();

				// 开始治疗
				if (!soldier1.being_healed) {
					soldier1.being_healed = true;
					soldier1.heal_start_time = current_time;
					soldier1.heal_progress = 0.0f;
					printf("玩家2开始治疗玩家1！\n");
				} else {
					// 更新治疗进度
					float heal_time = (float)(current_time - soldier1.heal_start_time) / 4000.0f; // 4秒完成治疗
					soldier1.heal_progress = heal_time > 1.0f ? 1.0f : heal_time;

					// 如果治疗完成
					if (soldier1.heal_progress >= 1.0f) {
						soldier1.health++; // 增加1点生命值
						soldier1.being_healed = false;
						soldier1.heal_progress = 0.0f;
						printf("玩家1被玩家2治疗，恢复1点生命值！当前生命值：%d\n", soldier1.health);
					}
				}
			}
		} else {
			// 如果松开L键，取消治疗
			if (soldier1.being_healed) {
				soldier1.being_healed = false;
				soldier1.heal_progress = 0.0f;
				printf("玩家2取消了对玩家1的治疗！\n");
			}
		}
	}
}

// 玩家1发射子弹
void fire_bullet1(int site) {
	// 获取当前升级加成
	Player1Upgrade upgrade = get_player1_upgrade();

	// 检查攻击冷却时间（应用攻击速度加成）
	DWORD current_time = GetTickCount();
	DWORD adjusted_cooldown = (DWORD)(PLAYER_ATTACK_COOLDOWN / upgrade.attack_speed_multiplier);
	if (current_time - last_player1_attack_time < adjusted_cooldown) {
		return; // 攻击冷却中，不能发射子弹
	}

	// 更新上次攻击时间
	last_player1_attack_time = current_time;

	// 创建子弹
	Bullet* new_bullet = (Bullet*)malloc(sizeof(Bullet));
	if (!new_bullet) {
		printf("内存分配失败！\n");
		return;
	}

	// 设置子弹初始位置（从士兵中心发射）
	new_bullet->x = soldier1.x + BLOCK_SIZE / 2 - BLOCK_SIZE / 4;
	new_bullet->y = soldier1.y + BLOCK_SIZE / 2 - BLOCK_SIZE / 4;

	// 根据士兵朝向设置子弹方向（应用速度加成）
	int base_speed = (int)(BULLET_SPEED * upgrade.bullet_speed_multiplier);
	int dir_x = 0, dir_y = 0;
	switch (site) {
		case 1: // 上
			dir_x = 0;
			dir_y = -base_speed;
			break;
		case 2: // 右
			dir_x = base_speed;
			dir_y = 0;
			break;
		case 3: // 下
			dir_x = 0;
			dir_y = base_speed;
			break;
		case 4: // 左
			dir_x = -base_speed;
			dir_y = 0;
			break;
		default:
			dir_x = 0;
			dir_y = -base_speed;
	}

	printf("玩家1发射了等级%d子弹！速度倍数:%.1fx\n", player1_level, upgrade.bullet_speed_multiplier);

	new_bullet->dir_x = dir_x;
	new_bullet->dir_y = dir_y;
	new_bullet->active = 1;

	// 将新子弹添加到链表头部
	new_bullet->next = bullet_head1;
	bullet_head1 = new_bullet;
}

// 玩家2放置炸弹
void place_bomb() {
	// 获取当前升级加成
	Player2Upgrade upgrade = get_player2_upgrade();

	// 检查攻击冷却时间（应用冷却时间加成）
	DWORD current_time = GetTickCount();
	DWORD adjusted_cooldown = (DWORD)(PLAYER_ATTACK_COOLDOWN * upgrade.bomb_cooldown_multiplier);
	if (current_time - last_player2_attack_time < adjusted_cooldown) {
		return; // 攻击冷却中，不能放置炸弹
	}

	// 检查当前炸弹数量是否已达到上限
	int current_bomb_count = 0;
	Bomb* count_bomb = bomb_head;
	while (count_bomb != NULL) {
		if (count_bomb->active) {
			current_bomb_count++;
		}
		count_bomb = count_bomb->next;
	}

	if (current_bomb_count >= upgrade.max_bombs) {
		return; // 已达到最大炸弹数量限制
	}

	// 更新上次攻击时间
	last_player2_attack_time = current_time;

	Bomb* new_bomb = (Bomb*)malloc(sizeof(Bomb));
	if (!new_bomb) {
		printf("内存分配失败！\n");
		return;
	}

	// 设置炸弹初始位置（以玩家2位置为中心）
	new_bomb->x = soldier2.x;
	new_bomb->y = soldier2.y;
	new_bomb->create_time = GetTickCount(); // 记录创建时间
	new_bomb->active = 1; // 激活状态

	// 将新炸弹添加到链表头部
	new_bomb->next = bomb_head;
	bomb_head = new_bomb;

	printf("玩家2放置了等级%d炸弹！位置：(%d, %d)，爆炸范围：%dx%d\n",
		player2_level, new_bomb->x, new_bomb->y, upgrade.explosion_size, upgrade.explosion_size);
}

// 创建以玩家为中心的爆炸区域
void create_explosion_area(int center_x, int center_y) {
	// 获取当前升级加成
	Player2Upgrade upgrade = get_player2_upgrade();

	// 创建新的轰炸区域
	BombingArea* new_bombing_area = (BombingArea*)malloc(sizeof(BombingArea));
	if (new_bombing_area == NULL) {
		printf("内存分配失败！\n");
		return;
	}

	// 初始化轰炸区域
	new_bombing_area->cells = NULL;
	new_bombing_area->cell_count = 0;
	new_bombing_area->active = 1;
	new_bombing_area->marked = 0;
	new_bombing_area->create_time = GetTickCount();
	new_bombing_area->mark_time = 0;

	// 确定爆炸范围大小（根据升级等级）
	int explosion_size = upgrade.explosion_size;

	// 计算爆炸区域的起始坐标（左上角）
	int start_x = center_x - (explosion_size / 2) * BLOCK_SIZE;
	int start_y = center_y - (explosion_size / 2) * BLOCK_SIZE;

	// 如果爆炸范围是偶数（4x4），需要调整起始位置
	if (explosion_size % 2 == 0) {
		start_x += BLOCK_SIZE / 2;
		start_y += BLOCK_SIZE / 2;
	}

	// 创建爆炸区域的所有格子
	for (int row = 0; row < explosion_size; row++) {
		for (int col = 0; col < explosion_size; col++) {
			int cell_x = start_x + col * BLOCK_SIZE;
			int cell_y = start_y + row * BLOCK_SIZE;

			// 确保格子在地图范围内
			if (cell_x >= 0 && cell_x < MAP_WIDTH && cell_y >= 0 && cell_y < MAP_HEIGHT) {
				// 创建新格子
				BombingCell* new_cell = (BombingCell*)malloc(sizeof(BombingCell));
				if (new_cell == NULL) {
					printf("内存分配失败！\n");
					continue;
				}

				// 设置格子坐标
				new_cell->x = cell_x;
				new_cell->y = cell_y;

				// 添加到链表头部
				new_cell->next = new_bombing_area->cells;
				new_bombing_area->cells = new_cell;
				new_bombing_area->cell_count++;
			}
		}
	}

	// 添加到轰炸区域链表头部
	new_bombing_area->next = bombing_area_head;
	bombing_area_head = new_bombing_area;

	printf("炸弹爆炸！创建了%dx%d的爆炸区域，格子数量：%d\n",
		explosion_size, explosion_size, new_bombing_area->cell_count);

	// 立即检查爆炸区域内的敌人和障碍物
	check_bombing_area();
}

// 更新炸弹状态
void update_bombs() {
	Bomb* current = bomb_head;
	Bomb* prev = NULL;
	DWORD current_time = GetTickCount();

	while (current != NULL) {
		// 检查炸弹是否已经存在3秒
		if (current->active && current_time - current->create_time >= 3000) { // 3秒后爆炸
			// 炸弹爆炸，创建随机形状的爆炸区域
			create_explosion_area(current->x, current->y);

			// 移除炸弹
			if (prev == NULL) {
				bomb_head = current->next;
				free(current);
				current = bomb_head;
			} else {
				prev->next = current->next;
				free(current);
				current = prev->next;
			}
		} else {
			prev = current;
			current = current->next;
		}
	}
}

// 绘制炸弹
void draw_bombs() {
	Bomb* current = bomb_head;
	DWORD current_time = GetTickCount();

	while (current != NULL) {
		if (current->active) {
			int screen_x = world_to_screen_x(current->x);
			int screen_y = world_to_screen_y(current->y);

			// 计算炸弹存在的时间
			DWORD bomb_time = current_time - current->create_time;

			// 根据炸弹存在时间改变颜色，越接近爆炸越红
			if (bomb_time < 1000) { // 第一秒
				setfillcolor(RGB(255, 200, 200)); // 浅红色
			} else if (bomb_time < 2000) { // 第二秒
				setfillcolor(RGB(255, 100, 100)); // 中红色
			} else { // 第三秒
				setfillcolor(RGB(255, 0, 0)); // 深红色

				// 在即将爆炸时闪烁
				if ((bomb_time / 100) % 2 == 0) {
					setfillcolor(RGB(255, 255, 0)); // 黄色闪烁
				}
			}

			// 绘制炸弹（中心点）
			fillrectangle(screen_x, screen_y,
				screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);

			// 绘制中心标记
			setlinecolor(RGB(255, 255, 0));
			line(screen_x, screen_y, screen_x + BLOCK_SIZE, screen_y + BLOCK_SIZE);
			line(screen_x, screen_y + BLOCK_SIZE, screen_x + BLOCK_SIZE, screen_y);

			// 绘制3x3或4x4爆炸范围预览
			setlinecolor(RGB(255, 0, 0));

			// 获取当前升级加成确定爆炸范围大小
			Player2Upgrade upgrade = get_player2_upgrade();
			int explosion_size = upgrade.explosion_size;

			// 计算爆炸区域的起始坐标（左上角）
			int start_x = current->x - (explosion_size / 2) * BLOCK_SIZE;
			int start_y = current->y - (explosion_size / 2) * BLOCK_SIZE;

			// 如果爆炸范围是偶数（4x4），需要调整起始位置
			if (explosion_size % 2 == 0) {
				start_x += BLOCK_SIZE / 2;
				start_y += BLOCK_SIZE / 2;
			}

			// 创建并绘制预览格子
			for (int row = 0; row < explosion_size; row++) {
				for (int col = 0; col < explosion_size; col++) {
					int cell_x = start_x + col * BLOCK_SIZE;
					int cell_y = start_y + row * BLOCK_SIZE;

					// 确保格子在地图范围内
					if (cell_x >= 0 && cell_x < MAP_WIDTH && cell_y >= 0 && cell_y < MAP_HEIGHT) {
						// 转换为屏幕坐标
						int px = world_to_screen_x(cell_x);
						int py = world_to_screen_y(cell_y);

						// 绘制格子边框
						setlinecolor(RGB(255, 0, 0));
						rectangle(px, py, px + BLOCK_SIZE, py + BLOCK_SIZE);

						// 绘制半透明填充
						setfillcolor(RGB(255, 100, 100));
						setfillstyle(BS_HATCHED, HS_DIAGCROSS);
						fillrectangle(px, py, px + BLOCK_SIZE, py + BLOCK_SIZE);
						setfillstyle(BS_SOLID, 0); // 使用0代替NULL作为第二个参数
					}
				}
			}
		}

		current = current->next;
	}
}

// 敌人发射子弹
void enemy_fire_bullet(Enemy* enemy) {
	if (enemy == NULL) return;

	// 根据敌人类型设置不同的攻击冷却时间
	DWORD attack_cooldown = ENEMY_ATTACK_COOLDOWN; // 默认冷却时间（500ms，一秒两次）

	// 狙击手攻击间隔更长
	if (enemy->type == ENEMY_SNIPER) {
		attack_cooldown = ENEMY_ATTACK_COOLDOWN * 4; // 2000ms，2秒一次
	}

	// 检查攻击冷却时间
	DWORD current_time = GetTickCount();
	if (current_time - enemy->last_attack_time < attack_cooldown) {
		return; // 攻击冷却中，不能发射子弹
	}

	// 更新上次攻击时间
	enemy->last_attack_time = current_time;

	EnemyBullet* new_bullet = (EnemyBullet*)malloc(sizeof(EnemyBullet));
	if (new_bullet == NULL) {
		printf("内存分配失败！\n");
		return;
	}

	// 设置子弹初始位置（从敌人中心发射）
	new_bullet->x = enemy->x + BLOCK_SIZE / 2 - BLOCK_SIZE / 4;
	new_bullet->y = enemy->y + BLOCK_SIZE / 2 - BLOCK_SIZE / 4;

	// 设置子弹类型（与敌人类型相同）
	new_bullet->type = enemy->type;

	// 检查玩家存活状态
	bool player1_alive = !soldier1.is_dead;
	bool player2_alive = !soldier2.is_dead;
	int dx, dy;
	double dist;

	// 如果两个玩家都死亡，敌人不会发射子弹
	if (!player1_alive && !player2_alive) {
		free(new_bullet); // 释放内存
		return;
	}

	// 如果只有一个玩家存活，敌人只会攻击该玩家
	if (player1_alive && !player2_alive) {
		// 计算朝向玩家1的方向
		dx = soldier1.x + BLOCK_SIZE / 2 - (enemy->x + BLOCK_SIZE / 2);
		dy = soldier1.y + BLOCK_SIZE / 2 - (enemy->y + BLOCK_SIZE / 2);

		if (enemy->type == ENEMY_SNIPER) {
			printf("狙击手敌人瞄准玩家1（唯一存活的玩家）\n");
		} else if (enemy->type == ENEMY_TANK) {
			printf("坦克敌人瞄准玩家1（唯一存活的玩家）\n");
		} else {
			printf("普通敌人瞄准玩家1（唯一存活的玩家）\n");
		}
	}
	else if (!player1_alive && player2_alive) {
		// 计算朝向玩家2的方向
		dx = soldier2.x + BLOCK_SIZE / 2 - (enemy->x + BLOCK_SIZE / 2);
		dy = soldier2.y + BLOCK_SIZE / 2 - (enemy->y + BLOCK_SIZE / 2);

		if (enemy->type == ENEMY_SNIPER) {
			printf("狙击手敌人瞄准玩家2（唯一存活的玩家）\n");
		} else if (enemy->type == ENEMY_TANK) {
			printf("坦克敌人瞄准玩家2（唯一存活的玩家）\n");
		} else {
			printf("普通敌人瞄准玩家2（唯一存活的玩家）\n");
		}
	}
	// 如果两个玩家都存活，随机选择攻击目标
	else {
		int target_player = rand() % 2; // 0表示玩家1，1表示玩家2

		if (target_player == 0) {
			// 计算朝向玩家1的方向
			dx = soldier1.x + BLOCK_SIZE / 2 - (enemy->x + BLOCK_SIZE / 2);
			dy = soldier1.y + BLOCK_SIZE / 2 - (enemy->y + BLOCK_SIZE / 2);

			if (enemy->type == ENEMY_SNIPER) {
				printf("狙击手敌人瞄准玩家1\n");
			} else if (enemy->type == ENEMY_TANK) {
				printf("坦克敌人瞄准玩家1\n");
			} else {
				printf("普通敌人瞄准玩家1\n");
			}
		} else {
			// 计算朝向玩家2的方向
			dx = soldier2.x + BLOCK_SIZE / 2 - (enemy->x + BLOCK_SIZE / 2);
			dy = soldier2.y + BLOCK_SIZE / 2 - (enemy->y + BLOCK_SIZE / 2);

			if (enemy->type == ENEMY_SNIPER) {
				printf("狙击手敌人瞄准玩家2\n");
			} else if (enemy->type == ENEMY_TANK) {
				printf("坦克敌人瞄准玩家2\n");
			} else {
				printf("普通敌人瞄准玩家2\n");
			}
		}
	}

	dist = sqrt(dx * dx + dy * dy);

	// 根据敌人类型设置不同的子弹速度和射程
	float speed_multiplier = 0.7f; // 默认速度（普通敌人）

	// 狙击手子弹速度更快
	if (enemy->type == ENEMY_SNIPER) {
		speed_multiplier = 1.5f; // 比玩家子弹还快50%
	}
	// 坦克子弹速度较慢但不会太慢
	else if (enemy->type == ENEMY_TANK) {
		speed_multiplier = 0.7f; // 比普通敌人稍慢，但不会太慢
	}

	// 标准化方向向量并设置速度
	if (dist > 0) {
		new_bullet->dir_x = (int)((double)dx / dist * (BULLET_SPEED * speed_multiplier));
		new_bullet->dir_y = (int)((double)dy / dist * (BULLET_SPEED * speed_multiplier));
	} else {
		// 如果距离为0（极少发生），随机方向
		new_bullet->dir_x = (rand() % 3 - 1) * BULLET_SPEED * speed_multiplier;
		new_bullet->dir_y = (rand() % 3 - 1) * BULLET_SPEED * speed_multiplier;
	}

	new_bullet->active = 1;
	new_bullet->create_time = GetTickCount();  // 记录创建时间

	// 将新子弹添加到链表头部
	new_bullet->next = enemy_bullet_head;
	enemy_bullet_head = new_bullet;

	// 根据敌人类型输出不同的发射信息
	if (enemy->type == ENEMY_SNIPER) {
		printf("狙击手敌人发射了高速子弹！位置：(%d, %d)\n", enemy->x, enemy->y);
	} else if (enemy->type == ENEMY_TANK) {
		printf("坦克敌人发射了子弹！位置：(%d, %d)\n", enemy->x, enemy->y);
	} else {
		printf("普通敌人发射了子弹！位置：(%d, %d)\n", enemy->x, enemy->y);
	}
}

// 更新敌人子弹位置并检测碰撞
void update_enemy_bullets() {
	EnemyBullet* current_bullet = enemy_bullet_head;
	EnemyBullet* prev_bullet = NULL;

	while (current_bullet != NULL) {
		if (current_bullet->active) {
			// 更新子弹位置
			current_bullet->x += current_bullet->dir_x;
			current_bullet->y += current_bullet->dir_y;

			// 检查子弹存在时间 - 根据敌人类型设置不同的生存时间
			DWORD current_time = GetTickCount();
			DWORD lifetime = 1500; // 默认生存时间（普通敌人：1.5秒）

			// 根据子弹类型设置不同的生存时间
			if (current_bullet->type == ENEMY_SNIPER) {
				lifetime = 2000; // 狙击手子弹：2秒
			} else if (current_bullet->type == ENEMY_TANK) {
				lifetime = 3000; // 坦克子弹：3秒（由于速度慢，所以生存时间更长）
			}

			if (current_time - current_bullet->create_time >= lifetime) {
				current_bullet->active = 0;

				// 根据子弹类型输出不同的消失信息
				if (current_bullet->type == ENEMY_SNIPER) {
					printf("狙击手敌人的高速子弹因存在时间过长而消失\n");
				} else if (current_bullet->type == ENEMY_TANK) {
					printf("坦克敌人的子弹因存在时间过长而消失\n");
				} else {
					printf("普通敌人的子弹因存在时间过长而消失\n");
				}
			}

			// 边界检查 - 使用地图边界而不是窗口边界
			if (current_bullet->x < 0 || current_bullet->x > MAP_WIDTH - BLOCK_SIZE/2 ||
				current_bullet->y < 0 || current_bullet->y > MAP_HEIGHT - BLOCK_SIZE/2) {
				current_bullet->active = 0;
			}

			// 障碍物碰撞检测
			Obstacle* obs_current = obstacle_head;
			int hit_obstacle = 0;
			while (obs_current != NULL && !hit_obstacle) {
				// 只有岩石障碍物会阻挡子弹，水域障碍物允许子弹通过
				if (obs_current->type == OBSTACLE_ROCK &&
					current_bullet->x < obs_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE/2 > obs_current->x &&
					current_bullet->y < obs_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE/2 > obs_current->y) {
					hit_obstacle = 1;
				}
				obs_current = obs_current->next;
			}
			if (hit_obstacle) {
				current_bullet->active = 0;
			}

			// 玩家1碰撞检测（只有在玩家1未死亡时才检测）
			if (!soldier1.is_dead &&
				current_bullet->x < soldier1.x + BLOCK_SIZE &&
				current_bullet->x + BLOCK_SIZE/2 > soldier1.x &&
				current_bullet->y < soldier1.y + BLOCK_SIZE &&
				current_bullet->y + BLOCK_SIZE/2 > soldier1.y) {

				// 检查玩家1是否处于无敌状态
				DWORD current_time = GetTickCount();
				if (current_time - soldier1.last_hit_time >= soldier1.invincible) {
					// 根据子弹类型输出不同的击中信息
					if (current_bullet->type == ENEMY_SNIPER) {
						printf("玩家1被狙击手敌人的高速子弹击中！\n");
					} else if (current_bullet->type == ENEMY_TANK) {
						printf("玩家1被坦克敌人的子弹击中！\n");
					} else {
						printf("玩家1被普通敌人的子弹击中！\n");
					}

					// 根据子弹类型造成不同的伤害
					int damage = 1; // 默认伤害（普通敌人）

					// 狙击手子弹伤害更高
					if (current_bullet->type == ENEMY_SNIPER) {
						damage = 2; // 狙击手造成2点伤害
						printf("狙击手的子弹造成了2点伤害！\n");
					}

					// 玩家1受伤，减少生命值
					soldier1.health -= damage;
					printf("玩家1失去%d条生命！剩余生命：%d\n", damage, soldier1.health);

					// 设置无敌时间（1.5秒）
					soldier1.invincible = 1500;
					soldier1.last_hit_time = current_time;

					// 如果生命值为0，玩家死亡但游戏不结束
					if (soldier1.health <= 0 && !soldier1.is_dead) {
						printf("玩家1死亡，等待救援！\n");
						soldier1.is_dead = true;
						soldier1.death_time = current_time;

						// 只有当两个玩家都死亡时，游戏才结束
						if (soldier1.is_dead && soldier2.is_dead) {
							printf("两名玩家均已阵亡，游戏结束！\n");
							game_over = 1;
						}
					}

					// 扣分
					if (player1_score >= 50) {
						player1_score -= 50;
						printf("玩家1失去50分，当前得分：%d\n", player1_score);
					}
				}

				// 移除子弹
				current_bullet->active = 0;
			}

			// 玩家2碰撞检测（只有在玩家2未死亡时才检测）
			if (!soldier2.is_dead &&
				current_bullet->x < soldier2.x + BLOCK_SIZE &&
				current_bullet->x + BLOCK_SIZE/2 > soldier2.x &&
				current_bullet->y < soldier2.y + BLOCK_SIZE &&
				current_bullet->y + BLOCK_SIZE/2 > soldier2.y) {

				// 检查玩家2是否处于无敌状态
				DWORD current_time = GetTickCount();
				if (current_time - soldier2.last_hit_time >= soldier2.invincible) {
					// 根据子弹类型输出不同的击中信息
					if (current_bullet->type == ENEMY_SNIPER) {
						printf("玩家2被狙击手敌人的高速子弹击中！\n");
					} else if (current_bullet->type == ENEMY_TANK) {
						printf("玩家2被坦克敌人的子弹击中！\n");
					} else {
						printf("玩家2被普通敌人的子弹击中！\n");
					}

					// 根据子弹类型造成不同的伤害
					int damage = 1; // 默认伤害（普通敌人）

					// 狙击手子弹伤害更高
					if (current_bullet->type == ENEMY_SNIPER) {
						damage = 2; // 狙击手造成2点伤害
						printf("狙击手的子弹造成了2点伤害！\n");
					}

					// 玩家2受伤，减少生命值
					soldier2.health -= damage;
					printf("玩家2失去%d条生命！剩余生命：%d\n", damage, soldier2.health);

					// 设置无敌时间（1.5秒）
					soldier2.invincible = 1500;
					soldier2.last_hit_time = current_time;

					// 如果生命值为0，玩家死亡但游戏不结束
					if (soldier2.health <= 0 && !soldier2.is_dead) {
						printf("玩家2死亡，等待救援！\n");
						soldier2.is_dead = true;
						soldier2.death_time = current_time;

						// 只有当两个玩家都死亡时，游戏才结束
						if (soldier1.is_dead && soldier2.is_dead) {
							printf("两名玩家均已阵亡，游戏结束！\n");
							game_over = 1;
						}
					}

					// 扣分
					if (player2_score >= 50) {
						player2_score -= 50;
						printf("玩家2失去50分，当前得分：%d\n", player2_score);
					}
				}

				// 移除子弹
				current_bullet->active = 0;
			}

			// 移除不活跃的子弹
			if (!current_bullet->active) {
				if (prev_bullet == NULL) {
					// 当前子弹是头节点
					enemy_bullet_head = current_bullet->next;
					free(current_bullet);
					current_bullet = enemy_bullet_head;
				} else {
					prev_bullet->next = current_bullet->next;
					free(current_bullet);
					current_bullet = prev_bullet->next;
				}
				continue;
			}

			prev_bullet = current_bullet;
		}
		current_bullet = current_bullet->next;
	}
}

// 绘制敌人子弹
void draw_enemy_bullets() {
	EnemyBullet* current = enemy_bullet_head;
	while (current != NULL) {
		if (current->active) {
			// 计算子弹在屏幕上的位置
			int screen_x = world_to_screen_x(current->x + BLOCK_SIZE / 4);
			int screen_y = world_to_screen_y(current->y + BLOCK_SIZE / 4);

			// 检查子弹是否在屏幕范围内
			if (screen_x >= -BLOCK_SIZE && screen_x <= WINDOW_WIDTH &&
				screen_y >= -BLOCK_SIZE && screen_y <= WINDOW_HEIGHT) {

				// 根据子弹类型设置不同的颜色和大小
				if (current->type == ENEMY_SNIPER) {
					// 狙击手子弹 - 亮橙色，较小但速度快
					setfillcolor(RGB(255, 200, 0)); // 亮橙色
					fillcircle(screen_x, screen_y, BLOCK_SIZE / 5);

					// 添加尾迹效果
					setfillcolor(RGB(255, 255, 0)); // 黄色尾迹
					fillcircle(screen_x - current->dir_x / 2, screen_y - current->dir_y / 2, BLOCK_SIZE / 8);
				}
				else if (current->type == ENEMY_TANK) {
					// 坦克子弹 - 深红色，较大但速度慢
					setfillcolor(RGB(180, 0, 0)); // 深红色
					fillcircle(screen_x, screen_y, BLOCK_SIZE / 4);

					// 添加内部细节
					setfillcolor(RGB(255, 100, 100)); // 浅红色内部
					fillcircle(screen_x, screen_y, BLOCK_SIZE / 8);
				}
				else {
					// 普通敌人子弹 - 浅红色，标准大小
					setfillcolor(RGB(255, 100, 100)); // 浅红色
					fillcircle(screen_x, screen_y, BLOCK_SIZE / 6);
				}
			}
		}
		current = current->next;
	}
}

// 更新玩家1子弹位置并检测碰撞
void update_bullets1() {
	Bullet* current_bullet = bullet_head1;
	Bullet* prev_bullet = NULL;

	while (current_bullet != NULL) {
		if (current_bullet->active) {
			// 更新子弹位置
			current_bullet->x += current_bullet->dir_x;
			current_bullet->y += current_bullet->dir_y;

			// 边界检查 - 使用地图边界而不是窗口边界
			if (current_bullet->x < 0 || current_bullet->x > MAP_WIDTH - BLOCK_SIZE/2 ||
				current_bullet->y < 0 || current_bullet->y > MAP_HEIGHT - BLOCK_SIZE/2) {
				current_bullet->active = 0;
			}

			// 障碍物碰撞检测
			Obstacle* obs_current = obstacle_head;
			int hit_obstacle = 0;
			while (obs_current != NULL && !hit_obstacle) {
				// 只有岩石障碍物会阻挡子弹，水域障碍物允许子弹通过
				if (obs_current->type == OBSTACLE_ROCK &&
					current_bullet->x < obs_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE/2 > obs_current->x &&
					current_bullet->y < obs_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE/2 > obs_current->y) {
					hit_obstacle = 1;
				}
				obs_current = obs_current->next;
			}
			if (hit_obstacle) {
				current_bullet->active = 0;
			}

			// 敌人碰撞检测
			Enemy* enemy_current = enemy_head;
			Enemy* prev_enemy = NULL;
			int hit_enemy = 0;

			while (enemy_current != NULL && !hit_enemy) {
				if (current_bullet->x < enemy_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE/2 > enemy_current->x &&
					current_bullet->y < enemy_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE/2 > enemy_current->y) {

					hit_enemy = 1;
					current_bullet->active = 0;

					// 根据敌人类型输出不同的信息
					if (enemy_current->type == ENEMY_SNIPER) {
						printf("玩家1击中狙击手敌人！\n");
					} else if (enemy_current->type == ENEMY_TANK) {
						printf("玩家1击中坦克敌人！\n");
					} else {
						printf("玩家1击中普通敌人！\n");
					}

					// 增加玩家1得分（根据敌人类型给予不同分数）
					int score_gain = 100; // 默认得分
					if (enemy_current->type == ENEMY_SNIPER) {
						score_gain = 150; // 狙击手敌人得分更高
					} else if (enemy_current->type == ENEMY_TANK) {
						score_gain = 50; // 坦克敌人每次击中得分较低，但总分更高
					}
					player1_score += score_gain;

					// 处理坦克敌人的多次击中逻辑
					if (enemy_current->type == ENEMY_TANK) {
						// 坦克敌人需要多次击中才能消灭
						enemy_current->health--;
						printf("坦克敌人受到伤害！剩余生命值：%d\n", enemy_current->health);

						// 如果坦克生命值仍然大于0，则不消灭它
						if (enemy_current->health > 0) {
							prev_enemy = enemy_current;
							enemy_current = enemy_current->next;
							printf("玩家1得分增加%d点，当前得分：%d\n", score_gain, player1_score);
							continue;
						}
					}

					// 增加玩家1击杀计数（用于子弹升级）
					player1_kills++;

					// 检查玩家升级
					check_player_upgrades();

					// 保存敌人类型以便在释放后使用
					EnemyType enemy_type = enemy_current->type;

					// 移除敌人节点
					if (prev_enemy == NULL) {
						// 当前敌人是头节点
						enemy_head = enemy_current->next;
					} else {
						prev_enemy->next = enemy_current->next;
					}
					Enemy* temp_enemy = enemy_current;
					enemy_current = enemy_current->next;
					free(temp_enemy);
					enemy_count--; // 减少敌人计数

					// 根据敌人类型输出不同的消灭信息
					if (enemy_type == ENEMY_SNIPER) {
						printf("狙击手敌人被玩家1消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player1_score, enemy_count, MAX_ENEMIES);
					} else if (enemy_type == ENEMY_TANK) {
						printf("坦克敌人被玩家1消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player1_score, enemy_count, MAX_ENEMIES);
					} else {
						printf("普通敌人被玩家1消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player1_score, enemy_count, MAX_ENEMIES);
					}
					continue;
				}

				prev_enemy = enemy_current;
				enemy_current = enemy_current->next;
			}

			// 移除不活跃的子弹
			if (!current_bullet->active) {
				if (prev_bullet == NULL) {
					// 当前子弹是头节点
					bullet_head1 = current_bullet->next;
					free(current_bullet);
					current_bullet = bullet_head1;
				} else {
					prev_bullet->next = current_bullet->next;
					free(current_bullet);
					current_bullet = prev_bullet->next;
				}
				continue;
			}

			prev_bullet = current_bullet;
		}
		current_bullet = current_bullet->next;
	}
}

// 更新玩家2子弹位置并检测碰撞
void update_bullets2() {
	Bullet* current_bullet = bullet_head2;
	Bullet* prev_bullet = NULL;

	while (current_bullet != NULL) {
		if (current_bullet->active) {
			// 更新子弹位置
			current_bullet->x += current_bullet->dir_x;
			current_bullet->y += current_bullet->dir_y;

			// 边界检查 - 使用地图边界而不是窗口边界
			if (current_bullet->x < 0 || current_bullet->x > MAP_WIDTH - BLOCK_SIZE/2 ||
				current_bullet->y < 0 || current_bullet->y > MAP_HEIGHT - BLOCK_SIZE/2) {
				current_bullet->active = 0;
			}

			// 障碍物碰撞检测
			Obstacle* obs_current = obstacle_head;
			int hit_obstacle = 0;
			while (obs_current != NULL && !hit_obstacle) {
				// 只有岩石障碍物会阻挡子弹，水域障碍物允许子弹通过
				if (obs_current->type == OBSTACLE_ROCK &&
					current_bullet->x < obs_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE/2 > obs_current->x &&
					current_bullet->y < obs_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE/2 > obs_current->y) {
					hit_obstacle = 1;
				}
				obs_current = obs_current->next;
			}
			if (hit_obstacle) {
				current_bullet->active = 0;
			}

			// 敌人碰撞检测
			Enemy* enemy_current = enemy_head;
			Enemy* prev_enemy = NULL;
			int hit_enemy = 0;

			while (enemy_current != NULL && !hit_enemy) {
				if (current_bullet->x < enemy_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE/2 > enemy_current->x &&
					current_bullet->y < enemy_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE/2 > enemy_current->y) {

					hit_enemy = 1;
					current_bullet->active = 0;

					// 根据敌人类型输出不同的信息
					if (enemy_current->type == ENEMY_SNIPER) {
						printf("玩家2击中狙击手敌人！\n");
					} else if (enemy_current->type == ENEMY_TANK) {
						printf("玩家2击中坦克敌人！\n");
					} else {
						printf("玩家2击中普通敌人！\n");
					}

					// 增加玩家2得分（根据敌人类型给予不同分数）
					int score_gain = 100; // 默认得分
					if (enemy_current->type == ENEMY_SNIPER) {
						score_gain = 150; // 狙击手敌人得分更高
					} else if (enemy_current->type == ENEMY_TANK) {
						score_gain = 50; // 坦克敌人每次击中得分较低，但总分更高
					}
					player2_score += score_gain;

					// 处理坦克敌人的多次击中逻辑
					if (enemy_current->type == ENEMY_TANK) {
						// 坦克敌人需要多次击中才能消灭
						enemy_current->health--;
						printf("坦克敌人受到伤害！剩余生命值：%d\n", enemy_current->health);

						// 如果坦克生命值仍然大于0，则不消灭它
						if (enemy_current->health > 0) {
							prev_enemy = enemy_current;
							enemy_current = enemy_current->next;
							printf("玩家2得分增加%d点，当前得分：%d\n", score_gain, player2_score);
							continue;
						}
					}

					// 增加玩家2击杀计数（用于炸弹升级）
					player2_kills++;

					// 检查玩家升级
					check_player_upgrades();

					// 保存敌人类型以便在释放后使用
					EnemyType enemy_type = enemy_current->type;

					// 移除敌人节点
					if (prev_enemy == NULL) {
						// 当前敌人是头节点
						enemy_head = enemy_current->next;
					} else {
						prev_enemy->next = enemy_current->next;
					}
					Enemy* temp_enemy = enemy_current;
					enemy_current = enemy_current->next;
					free(temp_enemy);
					enemy_count--; // 减少敌人计数

					// 根据敌人类型输出不同的消灭信息
					if (enemy_type == ENEMY_SNIPER) {
						printf("狙击手敌人被玩家2消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player2_score, enemy_count, MAX_ENEMIES);
					} else if (enemy_type == ENEMY_TANK) {
						printf("坦克敌人被玩家2消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player2_score, enemy_count, MAX_ENEMIES);
					} else {
						printf("普通敌人被玩家2消灭！得分增加%d点，当前得分：%d，剩余敌人：%d/%d\n",
							score_gain, player2_score, enemy_count, MAX_ENEMIES);
					}
					continue;
				}

				prev_enemy = enemy_current;
				enemy_current = enemy_current->next;
			}

			// 移除不活跃的子弹
			if (!current_bullet->active) {
				if (prev_bullet == NULL) {
					// 当前子弹是头节点
					bullet_head2 = current_bullet->next;
					free(current_bullet);
					current_bullet = bullet_head2;
				} else {
					prev_bullet->next = current_bullet->next;
					free(current_bullet);
					current_bullet = prev_bullet->next;
				}
				continue;
			}

			prev_bullet = current_bullet;
		}
		current_bullet = current_bullet->next;
	}
}

// 绘制玩家1子弹
void draw_bullets1() {
	Bullet* current = bullet_head1;
	while (current != NULL) {
		if (current->active) {
			// 计算子弹在屏幕上的位置（使用通用的坐标转换函数）
			int screen_x = world_to_screen_x(current->x + BLOCK_SIZE / 4);
			int screen_y = world_to_screen_y(current->y + BLOCK_SIZE / 4);

			// 检查子弹是否在屏幕范围内（整个屏幕，不仅仅是左半部分）
			if (screen_x >= -BLOCK_SIZE/2 && screen_x <= WINDOW_WIDTH &&
				screen_y >= -BLOCK_SIZE/2 && screen_y <= WINDOW_HEIGHT) {

				// 绘制子弹
				setfillcolor(YELLOW);
				fillcircle(screen_x, screen_y, BLOCK_SIZE / 6);
			}
		}
		current = current->next;
	}
}

// 绘制玩家2子弹
void draw_bullets2() {
	Bullet* current = bullet_head2;
	while (current != NULL) {
		if (current->active) {
			// 计算子弹在屏幕上的位置（使用通用的坐标转换函数）
			int screen_x = world_to_screen_x(current->x + BLOCK_SIZE / 4);
			int screen_y = world_to_screen_y(current->y + BLOCK_SIZE / 4);

			// 检查子弹是否在屏幕范围内（整个屏幕，不仅仅是右半部分）
			if (screen_x >= -BLOCK_SIZE/2 && screen_x <= WINDOW_WIDTH &&
				screen_y >= -BLOCK_SIZE/2 && screen_y <= WINDOW_HEIGHT) {

				// 绘制子弹
				setfillcolor(YELLOW);
				fillcircle(screen_x, screen_y, BLOCK_SIZE / 6);
			}
		}
		current = current->next;
	}
}

// 检查和更新狂暴模式状态
void check_rage_mode() {
    DWORD current_time = GetTickCount();

    // 如果游戏刚开始，初始化上次狂暴模式时间
    if (last_rage_mode_time == 0) {
        last_rage_mode_time = current_time;
        return;
    }

    // 检查是否需要开始狂暴模式
    if (!rage_mode_active && current_time - last_rage_mode_time >= RAGE_MODE_INTERVAL) {
        start_rage_mode();
    }

    // 检查是否需要结束狂暴模式
    if (rage_mode_active && current_time - rage_mode_start_time >= RAGE_MODE_DURATION) {
        end_rage_mode();
    }
}

// 开始狂暴模式
void start_rage_mode() {
    rage_mode_active = true;
    rage_mode_start_time = GetTickCount();
    rage_mode_count++; // 增加狂暴模式计数

    // 计算增强百分比（随着狂暴次数增加而增加）
    float base_increase = 0.2f; // 基础增强20%
    float additional_increase = 0.05f * (rage_mode_count - 1); // 每次狂暴额外增加5%
    float total_increase = base_increase + additional_increase;

    // 限制最大增强幅度为100%
    if (total_increase > 1.0f) {
        total_increase = 1.0f;
    }

    // 增加敌人生成速率
    current_enemy_spawn_rate = NORMAL_ENEMY_SPAWN_RATE * (1 + total_increase * 2); // 敌人数量增加更多

    // 立即生成一批敌人
    int extra_enemies = 5 + rage_mode_count; // 额外生成的敌人数量
    for (int i = 0; i < extra_enemies && enemy_count < MAX_ENEMIES; i++) {
        spawn_enemy();
    }

    // 输出狂暴模式开始信息
    printf("狂暴模式开始！第%d次狂暴，敌人属性提升%.0f%%！\n",
           rage_mode_count, total_increase * 100);
}

// 结束狂暴模式
void end_rage_mode() {
    rage_mode_active = false;
    last_rage_mode_time = GetTickCount(); // 更新上次狂暴模式时间

    // 恢复敌人生成速率
    current_enemy_spawn_rate = NORMAL_ENEMY_SPAWN_RATE;

    // 输出狂暴模式结束信息
    printf("狂暴模式结束！敌人属性恢复正常。\n");
}

// 获取当前狂暴模式的增强系数
float get_rage_mode_multiplier() {
    if (!rage_mode_active) {
        return 1.0f; // 非狂暴模式，返回正常系数
    }

    // 计算增强百分比（随着狂暴次数增加而增加）
    float base_increase = 0.2f; // 基础增强20%
    float additional_increase = 0.05f * (rage_mode_count - 1); // 每次狂暴额外增加5%
    float total_increase = base_increase + additional_increase;

    // 限制最大增强幅度为100%
    if (total_increase > 1.0f) {
        total_increase = 1.0f;
    }

    return 1.0f + total_increase; // 返回增强后的系数
}

// 绘制狂暴模式进度条 - 单独的函数，在游戏循环的最后调用
void draw_rage_mode_progress_bar() {
    // 保存当前工作区
    HWND hwnd = GetHWnd();
    HDC hdc = GetDC(hwnd);
    SetWorkingImage(NULL); // 切换到主窗口绘图

    // 获取当前时间
    DWORD current_time = GetTickCount();

    // 如果处于狂暴模式，绘制红色边框效果
    if (rage_mode_active) {
        // 计算闪烁强度 - 使用正弦函数使边框亮度呈现波动效果
        float intensity = (float)(sin(current_time / 100.0) * 0.3 + 0.7); // 0.4 到 1.0 之间波动

        // 根据狂暴次数增加红色边框的亮度和厚度
        int border_thickness = 10 + rage_mode_count * 2; // 基础厚度10像素，每次狂暴增加2像素
        if (border_thickness > 30) border_thickness = 30; // 最大厚度限制为30像素

        // 计算红色边框的颜色 - 随着狂暴次数增加，颜色从红色向白色过渡
        int red = 255;
        int green = 0 + (rage_mode_count - 1) * 20; // 每次狂暴增加一些绿色成分
        if (green > 100) green = 100; // 绿色成分最大为100
        int blue = 0 + (rage_mode_count - 1) * 10; // 每次狂暴增加一些蓝色成分
        if (blue > 50) blue = 50; // 蓝色成分最大为50

        // 应用闪烁强度
        red = (int)(red * intensity);
        green = (int)(green * intensity);
        blue = (int)(blue * intensity);

        // 设置边框颜色和样式
        setlinecolor(RGB(red, green, blue));
        setlinestyle(PS_SOLID, border_thickness);

        // 绘制四条边框线
        // 上边框
        line(0, 0, WINDOW_WIDTH, 0);
        // 右边框
        line(WINDOW_WIDTH, 0, WINDOW_WIDTH, WINDOW_HEIGHT);
        // 下边框
        line(WINDOW_WIDTH, WINDOW_HEIGHT, 0, WINDOW_HEIGHT);
        // 左边框
        line(0, WINDOW_HEIGHT, 0, 0);

        // 恢复默认线条样式
        setlinestyle(PS_SOLID, 1);
    }

    // 进度条尺寸和位置 - 使用固定屏幕坐标
    int progress_bar_width = 400; // 进度条宽度
    int progress_bar_height = 30; // 增加高度使其更明显
    int progress_bar_x = (WINDOW_WIDTH - progress_bar_width) / 2; // 居中显示
    int progress_bar_y = 80; // 顶部位置

    // 绘制进度条背景 - 使用更明显的颜色和边框
    setfillcolor(RGB(30, 30, 30)); // 更深的灰色背景
    setlinecolor(RGB(200, 200, 200)); // 亮灰色边框
    setlinestyle(PS_SOLID, 3); // 加粗边框
    fillrectangle(progress_bar_x, progress_bar_y,
        progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 根据当前状态计算进度并绘制进度条
    float progress = 0.0f;
    char time_text[50];

    if (rage_mode_active) {
        // 狂暴模式激活中，显示剩余时间
        progress = 1.0f - (float)(current_time - rage_mode_start_time) / RAGE_MODE_DURATION;
        if (progress < 0.0f) progress = 0.0f;

        // 计算剩余秒数
        int seconds_left = (RAGE_MODE_DURATION - (current_time - rage_mode_start_time)) / 1000;
        if (seconds_left < 0) seconds_left = 0;

        // 设置进度条颜色为红色（狂暴模式）- 使用更明亮的红色
        setfillcolor(RGB(255, 50, 50)); // 亮红色

        // 格式化时间文本
        sprintf(time_text, "狂暴模式剩余: %d秒", seconds_left);
    } else {
        // 等待下一次狂暴模式，显示准备进度
        progress = (float)(current_time - last_rage_mode_time) / RAGE_MODE_INTERVAL;
        if (progress > 1.0f) progress = 1.0f;

        // 计算剩余秒数
        int seconds_left = (RAGE_MODE_INTERVAL - (current_time - last_rage_mode_time)) / 1000;
        if (seconds_left < 0) seconds_left = 0;

        // 设置进度条颜色为黄色（准备中）
        setfillcolor(RGB(255, 255, 0)); // 黄色

        // 格式化时间文本
        sprintf(time_text, "下次狂暴模式: %d秒", seconds_left);
    }

    // 绘制进度条前景
    fillrectangle(progress_bar_x + 2, progress_bar_y + 2,
        progress_bar_x + 2 + (int)((progress_bar_width - 4) * progress), progress_bar_y + progress_bar_height - 2);

    // 绘制进度条边框
    setlinecolor(WHITE);
    setlinestyle(PS_SOLID, 3); // 加粗边框
    rectangle(progress_bar_x, progress_bar_y,
        progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 显示时间文本 - 使用更大的字体
    settextcolor(WHITE);
    settextstyle(24, 0, _T("宋体")); // 增大字体
    outtextxy(progress_bar_x + (progress_bar_width - textwidth(time_text)) / 2,
        progress_bar_y + progress_bar_height + 10, time_text);

    // 如果处于狂暴模式，添加闪烁的"狂暴模式"文字
    if (rage_mode_active && (current_time / 300) % 2 == 0) { // 加快闪烁频率
        settextcolor(RGB(255, 50, 50)); // 亮红色
        settextstyle(36, 0, _T("宋体")); // 更大的字体
        char rage_text[50];
        sprintf(rage_text, "第（%d）次狂暴激活中!", rage_mode_count);
        outtextxy(progress_bar_x + (progress_bar_width - textwidth(rage_text)) / 2,
            progress_bar_y - 50, rage_text);
    }

    // 恢复默认设置
    setlinestyle(PS_SOLID, 1);
    settextstyle(20, 0, _T("宋体"));

    // 释放设备上下文
    ReleaseDC(hwnd, hdc);
}

// 绘制按钮
void draw_button(int x, int y, int width, int height, const char* text, bool highlighted) {
    // 设置按钮颜色 - 使用更深的蓝色调，与亮色背景形成对比
    if (highlighted) {
        setfillcolor(RGB(50, 120, 220)); // 高亮颜色 - 明亮的蓝色
    } else {
        setfillcolor(RGB(30, 80, 150));  // 正常颜色 - 深蓝色
    }

    // 绘制按钮背景
    fillroundrect(x, y, x + width, y + height, 15, 15); // 增加圆角半径，使按钮更现代

    // 绘制按钮边框 - 使用稍微亮一点的颜色
    if (highlighted) {
        setlinecolor(RGB(100, 180, 255)); // 高亮边框 - 亮蓝色
    } else {
        setlinecolor(RGB(80, 140, 200));  // 正常边框 - 中蓝色
    }
    roundrect(x, y, x + width, y + height, 15, 15);

    // 绘制按钮文本 - 使用白色文本，在深色按钮上清晰可见
    settextcolor(WHITE);
    settextstyle(30, 0, _T("宋体"));
    int text_width = textwidth(text);
    int text_height = textheight(text);
    outtextxy(x + (width - text_width) / 2, y + (height - text_height) / 2, text);
}

// 显示开始菜单
void show_start_menu() {
    // 清屏
    cleardevice();

    // 设置亮色背景 - 使用淡蓝色调
    setbkcolor(RGB(220, 240, 255)); // 淡蓝色背景
    cleardevice();

    // 绘制标题 - 使用深蓝色文本，在亮色背景上更清晰
    settextcolor(RGB(0, 50, 100)); // 深蓝色
    settextstyle(60, 0, _T("宋体"));
    const char* title = "无尽模式";
    int title_width = textwidth(title);
    outtextxy((WINDOW_WIDTH - title_width) / 2, 100, title);

    // 按钮尺寸和位置
    int button_width = 300;
    int button_height = 60;
    int button_x = (WINDOW_WIDTH - button_width) / 2;
    int button_y1 = 250; // 调整位置，为第三个按钮腾出空间
    int button_y2 = 350;
    int button_y3 = 450; // 退出游戏按钮位置

    // 鼠标状态
    bool button1_highlighted = false;
    bool button2_highlighted = false;
    bool button3_highlighted = false; // 退出游戏按钮高亮状态
    bool menu_active = true;
    bool has_save = false;

    // 检查是否存在存档
    FILE* file = fopen("game_save.dat", "rb");
    if (file) {
        has_save = true;
        fclose(file);
    }

    // 菜单循环
    while (menu_active) {
        // 检查鼠标消息
        ExMessage msg;
        while (peekmessage(&msg, EX_MOUSE)) {
            // 检查鼠标是否在按钮上
            button1_highlighted = (msg.x >= button_x && msg.x <= button_x + button_width &&
                                  msg.y >= button_y1 && msg.y <= button_y1 + button_height);

            button2_highlighted = has_save && (msg.x >= button_x && msg.x <= button_x + button_width &&
                                  msg.y >= button_y2 && msg.y <= button_y2 + button_height);

            button3_highlighted = (msg.x >= button_x && msg.x <= button_x + button_width &&
                                  msg.y >= button_y3 && msg.y <= button_y3 + button_height);

            // 检查鼠标点击
            if (msg.message == WM_LBUTTONDOWN) {
                if (button1_highlighted) {
                    // 新游戏按钮被点击
                    menu_active = false;
                    return; // 返回开始新游戏
                } else if (button2_highlighted) {
                    // 继续游戏按钮被点击
                    if (load_game()) {
                        menu_active = false;
                        game_loop(); // 直接进入游戏循环
                        exit(0); // 游戏结束后退出
                    }
                } else if (button3_highlighted) {
                    // 退出游戏按钮被点击
                    closegraph(); // 关闭图形界面
                    exit(0); // 退出程序
                }
            }
        }

        // 绘制按钮
        draw_button(button_x, button_y1, button_width, button_height, "新的开始", button1_highlighted);

        // 只有存在存档时才显示"继续游戏"按钮
        if (has_save) {
            draw_button(button_x, button_y2, button_width, button_height, "继续游戏", button2_highlighted);
        } else {
            // 显示提示信息 - 使用深灰色，在亮色背景上更清晰
            settextcolor(RGB(80, 80, 100));
            settextstyle(20, 0, _T("宋体"));
            const char* no_save_text = "没有可用的存档";
            int text_width = textwidth(no_save_text);
            outtextxy((WINDOW_WIDTH - text_width) / 2, button_y2 + 20, no_save_text);
        }

        // 绘制退出游戏按钮
        draw_button(button_x, button_y3, button_width, button_height, "退出游戏", button3_highlighted);

        // 显示操作提示 - 使用深蓝色，在亮色背景上更清晰
        settextcolor(RGB(20, 60, 120));
        settextstyle(20, 0, _T("宋体"));
        const char* hint_text = "在游戏中按 R 键保存游戏";
        int hint_width = textwidth(hint_text);
        outtextxy((WINDOW_WIDTH - hint_width) / 2, 550, hint_text);

        // 刷新屏幕
        FlushBatchDraw();

        // 延时
        Sleep(10);
    }
}

// 保存游戏状态
void save_game() {
    // 创建存档结构体
    GameSave save;

    // 保存玩家状态
    save.soldier1_x = soldier1.x;
    save.soldier1_y = soldier1.y;
    save.soldier1_site = soldier1.site;
    save.soldier1_health = soldier1.health;
    save.soldier1_is_dead = soldier1.is_dead;
    save.soldier1_invincible = soldier1.invincible;
    save.soldier1_last_hit_time = soldier1.last_hit_time;
    save.soldier1_death_time = soldier1.death_time;
    save.soldier1_being_rescued = soldier1.being_rescued;
    save.soldier1_being_healed = soldier1.being_healed;
    save.soldier1_heal_start_time = soldier1.heal_start_time;
    save.soldier1_heal_progress = soldier1.heal_progress;

    save.soldier2_x = soldier2.x;
    save.soldier2_y = soldier2.y;
    save.soldier2_site = soldier2.site;
    save.soldier2_health = soldier2.health;
    save.soldier2_is_dead = soldier2.is_dead;
    save.soldier2_invincible = soldier2.invincible;
    save.soldier2_last_hit_time = soldier2.last_hit_time;
    save.soldier2_death_time = soldier2.death_time;
    save.soldier2_being_rescued = soldier2.being_rescued;
    save.soldier2_being_healed = soldier2.being_healed;
    save.soldier2_heal_start_time = soldier2.heal_start_time;
    save.soldier2_heal_progress = soldier2.heal_progress;

    // 保存游戏状态
    save.player1_score = player1_score;
    save.player2_score = player2_score;
    save.player1_kills = player1_kills;
    save.player2_kills = player2_kills;
    save.player1_level = player1_level;
    save.player2_level = player2_level;
    save.level = level;
    save.enemy_count = enemy_count;
    save.obstacle_count = obstacle_count;

    // 保存狂暴模式状态
    save.rage_mode_active = rage_mode_active;
    save.rage_mode_count = rage_mode_count;
    save.rage_mode_start_time = rage_mode_start_time;
    save.last_rage_mode_time = last_rage_mode_time;
    save.current_enemy_spawn_rate = current_enemy_spawn_rate;

    // 保存时间戳
    save.save_time = GetTickCount();

    // 写入文件
    FILE* file = fopen("game_save.dat", "wb");
    if (file) {
        fwrite(&save, sizeof(GameSave), 1, file);
        fclose(file);

        // 显示保存成功消息
        settextcolor(GREEN);
        settextstyle(30, 0, _T("宋体"));
        const char* save_text = "游戏已保存";
        int text_width = textwidth(save_text);
        outtextxy((WINDOW_WIDTH - text_width) / 2, 50, save_text);
        FlushBatchDraw();
        Sleep(1000); // 显示1秒
    } else {
        // 显示保存失败消息
        settextcolor(RED);
        settextstyle(30, 0, _T("宋体"));
        const char* error_text = "保存失败";
        int text_width = textwidth(error_text);
        outtextxy((WINDOW_WIDTH - text_width) / 2, 50, error_text);
        FlushBatchDraw();
        Sleep(1000); // 显示1秒
    }
}

// 加载游戏状态
bool load_game() {
    GameSave save;

    // 读取文件
    FILE* file = fopen("game_save.dat", "rb");
    if (!file) {
        return false;
    }

    // 读取存档数据
    if (fread(&save, sizeof(GameSave), 1, file) != 1) {
        fclose(file);
        return false;
    }

    fclose(file);

    // 恢复玩家状态
    soldier1.x = save.soldier1_x;
    soldier1.y = save.soldier1_y;
    soldier1.site = save.soldier1_site;
    soldier1.health = save.soldier1_health;
    soldier1.is_dead = save.soldier1_is_dead;
    soldier1.invincible = save.soldier1_invincible;
    soldier1.last_hit_time = save.soldier1_last_hit_time;
    soldier1.death_time = save.soldier1_death_time;
    soldier1.being_rescued = save.soldier1_being_rescued;
    soldier1.being_healed = save.soldier1_being_healed;
    soldier1.heal_start_time = save.soldier1_heal_start_time;
    soldier1.heal_progress = save.soldier1_heal_progress;

    soldier2.x = save.soldier2_x;
    soldier2.y = save.soldier2_y;
    soldier2.site = save.soldier2_site;
    soldier2.health = save.soldier2_health;
    soldier2.is_dead = save.soldier2_is_dead;
    soldier2.invincible = save.soldier2_invincible;
    soldier2.last_hit_time = save.soldier2_last_hit_time;
    soldier2.death_time = save.soldier2_death_time;
    soldier2.being_rescued = save.soldier2_being_rescued;
    soldier2.being_healed = save.soldier2_being_healed;
    soldier2.heal_start_time = save.soldier2_heal_start_time;
    soldier2.heal_progress = save.soldier2_heal_progress;

    // 恢复游戏状态
    player1_score = save.player1_score;
    player2_score = save.player2_score;
    player1_kills = save.player1_kills;
    player2_kills = save.player2_kills;
    player1_level = save.player1_level;
    player2_level = save.player2_level;
    level = save.level;

    // 恢复狂暴模式状态
    rage_mode_active = save.rage_mode_active;
    rage_mode_count = save.rage_mode_count;
    rage_mode_start_time = save.rage_mode_start_time;
    last_rage_mode_time = save.last_rage_mode_time;
    current_enemy_spawn_rate = save.current_enemy_spawn_rate;

    // 清空现有敌人和障碍物
    Enemy* current_enemy = enemy_head;
    while (current_enemy != NULL) {
        Enemy* temp = current_enemy;
        current_enemy = current_enemy->next;
        free(temp);
    }
    enemy_head = NULL;
    enemy_count = 0;

    Obstacle* current_obstacle = obstacle_head;
    while (current_obstacle != NULL) {
        Obstacle* temp = current_obstacle;
        current_obstacle = current_obstacle->next;
        free(temp);
    }
    obstacle_head = NULL;
    obstacle_count = 0;

    // 重新生成障碍物
    spawn_obstacle(save.obstacle_count);

    // 生成一些敌人
    for (int i = 0; i < save.enemy_count; i++) {
        spawn_enemy();
    }

    // 显示加载成功消息
    settextcolor(GREEN);
    settextstyle(30, 0, _T("宋体"));
    const char* load_text = "游戏已加载";
    int text_width = textwidth(load_text);
    outtextxy((WINDOW_WIDTH - text_width) / 2, 50, load_text);
    FlushBatchDraw();
    Sleep(1000); // 显示1秒

    return true;
}

// 主函数
int main() {
    // 隐藏控制台窗口
    HWND hwnd = GetConsoleWindow();
    ShowWindow(hwnd, SW_HIDE);

    // 初始化游戏
    init_game();

    // 游戏主循环
    while (true) {
        // 显示开始菜单
        show_start_menu();

        // 显示背景故事
        show_story();

        // 开始游戏
        spawn_obstacle(200);  // 从50增加到200个障碍物
        game_loop();

        // game_loop结束后，game_over_screen会重置游戏状态并返回
        // 循环继续，再次显示开始菜单
    }

    return 0;
}

// 升级系统实现函数

// 安全的数组访问函数
int get_safe_upgrade_kills(int level) {
    if (level < 0) return 0;
    if (level > MAX_LEVEL) return UPGRADE_KILLS[MAX_LEVEL];
    return UPGRADE_KILLS[level];
}

// 安全的进度计算函数
float calculate_safe_progress(int current_kills, int level) {
    if (level >= MAX_LEVEL) return 1.0f;

    // 获取当前等级和下一等级所需的击杀数
    int current_level_kills = get_safe_upgrade_kills(level);
    int next_level_kills = get_safe_upgrade_kills(level + 1);
    int kills_needed = next_level_kills - current_level_kills;

    printf("DEBUG: calculate_safe_progress() - 当前击杀=%d, 等级=%d, 当前等级需要=%d, 下一等级需要=%d\n",
           current_kills, level, current_level_kills, next_level_kills);

    if (kills_needed <= 0) return 1.0f;  // 防止除零

    int kills_for_next_level = current_kills - current_level_kills;
    float progress = (float)kills_for_next_level / kills_needed;

    printf("DEBUG: 升级进度计算 - 超出当前等级的击杀=%d, 需要击杀=%d, 进度=%.2f\n",
           kills_for_next_level, kills_needed, progress);

    // 限制范围
    if (progress > 1.0f) progress = 1.0f;
    if (progress < 0.0f) progress = 0.0f;

    return progress;
}

// 根据击杀数获取等级
int get_player_level(int kills) {
    printf("DEBUG: get_player_level() - 击杀数=%d\n", kills);
    for (int i = MAX_LEVEL; i >= 1; i--) {
        int required_kills = get_safe_upgrade_kills(i);
        printf("DEBUG: 检查等级%d, 需要击杀数=%d\n", i, required_kills);
        if (kills >= required_kills) {
            printf("DEBUG: 返回等级=%d\n", i);
            return i;
        }
    }
    printf("DEBUG: 返回最低等级=1\n");
    return 1; // 最低等级
}

// 检查玩家升级
void check_player_upgrades() {
    // 检查玩家1升级
    int new_level1 = get_player_level(player1_kills);

    // 调试输出
    printf("DEBUG: 玩家1击杀数=%d, 当前等级=%d, 计算等级=%d\n", player1_kills, player1_level, new_level1);

    if (new_level1 > player1_level) {
        int old_level = player1_level;
        player1_level = new_level1;
        Player1Upgrade upgrade = get_player1_upgrade();

        // 应用生命值奖励
        if (upgrade.health_bonus > 0) {
            soldier1.health += upgrade.health_bonus;
            printf("玩家1获得额外生命值：+%d，当前生命：%d\n", upgrade.health_bonus, soldier1.health);
        }

        printf("=== 玩家1升级！===\n");
        printf("等级: %d -> %d\n", old_level, player1_level);
        printf("子弹速度: %.1fx\n", upgrade.bullet_speed_multiplier);
        printf("子弹伤害: %.1fx\n", upgrade.bullet_damage_multiplier);
        printf("子弹穿透: %d个敌人\n", upgrade.bullet_penetration);
        printf("攻击速度: %.1fx\n", upgrade.attack_speed_multiplier);
        printf("额外生命: +%d\n", upgrade.health_bonus);
        printf("移动速度: %.1fx\n", upgrade.movement_speed_multiplier);
        printf("==================\n");
    }

    // 检查玩家2升级
    int new_level2 = get_player_level(player2_kills);

    // 调试输出
    printf("DEBUG: 玩家2击杀数=%d, 当前等级=%d, 计算等级=%d\n", player2_kills, player2_level, new_level2);

    if (new_level2 > player2_level) {
        int old_level = player2_level;
        player2_level = new_level2;
        Player2Upgrade upgrade = get_player2_upgrade();

        // 应用生命值奖励
        if (upgrade.health_bonus > 0) {
            soldier2.health += upgrade.health_bonus;
            printf("玩家2获得额外生命值：+%d，当前生命：%d\n", upgrade.health_bonus, soldier2.health);
        }

        printf("=== 玩家2升级！===\n");
        printf("等级: %d -> %d\n", old_level, player2_level);
        printf("爆炸范围: %dx%d\n", upgrade.explosion_size, upgrade.explosion_size);
        printf("爆炸伤害: %.1fx\n", upgrade.explosion_damage_multiplier);
        printf("炸弹冷却: %.0f%%\n", upgrade.bomb_cooldown_multiplier * 100);
        printf("最大炸弹数: %d个\n", upgrade.max_bombs);
        printf("额外生命: +%d\n", upgrade.health_bonus);
        printf("移动速度: %.1fx\n", upgrade.movement_speed_multiplier);
        printf("==================\n");
    }
}

// 获取玩家1当前升级加成
Player1Upgrade get_player1_upgrade() {
    int level_index = (player1_level <= MAX_LEVEL) ? player1_level : MAX_LEVEL;
    return PLAYER1_UPGRADES[level_index];
}

// 获取玩家2当前升级加成
Player2Upgrade get_player2_upgrade() {
    int level_index = (player2_level <= MAX_LEVEL) ? player2_level : MAX_LEVEL;
    return PLAYER2_UPGRADES[level_index];
}

// 这个函数已被移除，等级加成信息现在显示在小地图下方

// 这个函数已被移除，进度条现在显示在小地图下方

// 这个函数已被移除，等级信息现在显示在小地图下方

// 这个函数已被移除，等级信息现在显示在小地图下方

// 这个函数已被移除，等级信息现在显示在小地图下方

// 绘制玩家1小地图等级信息
void draw_minimap1_level_info(int minimap_x, int minimap_y, int minimap_size) {
    int info_start_y = minimap_y + minimap_size + 120; // 在键位说明下方开始，留出更多空间
    int line_height = 14; // 减少行高使布局更紧凑

    // 获取升级加成
    Player1Upgrade upgrade1 = get_player1_upgrade();

    // 显示等级信息标题
    settextcolor(RGB(100, 149, 237)); // 蓝色标题
    settextstyle(14, 0, _T("宋体"));
    outtextxy(minimap_x, info_start_y, "【等级加成】");

    // 显示等级和击杀进度
    settextcolor(RGB(255, 215, 0)); // 金色文字
    settextstyle(12, 0, _T("宋体"));
    char level_text[50];
    if (player1_level <= MAX_LEVEL) {
        snprintf(level_text, sizeof(level_text), "等级: %d", player1_level);
    } else {
        snprintf(level_text, sizeof(level_text), "等级: MAX");
    }
    outtextxy(minimap_x, info_start_y + line_height, level_text);

    // 显示击杀进度
    settextcolor(RGB(173, 216, 230)); // 浅蓝色文字
    char kills_text[50];
    int next_level_kills = get_safe_upgrade_kills(player1_level + 1);
    if (player1_level < MAX_LEVEL) {
        snprintf(kills_text, sizeof(kills_text), "击杀: %d/%d", player1_kills, next_level_kills);
    } else {
        snprintf(kills_text, sizeof(kills_text), "击杀: %d (MAX)", player1_kills);
    }
    outtextxy(minimap_x, info_start_y + line_height * 2, kills_text);

    // 绘制升级进度条
    int progress_bar_width = 180; // 稍微小于小地图宽度
    int progress_bar_height = 6;
    int progress_bar_x = minimap_x;
    int progress_bar_y = info_start_y + line_height * 3;

    // 计算进度（使用安全函数）
    float progress = calculate_safe_progress(player1_kills, player1_level);

    // 绘制进度条背景
    setfillcolor(RGB(50, 50, 50));
    fillrectangle(progress_bar_x, progress_bar_y, progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 绘制进度条前景
    if (progress > 0) {
        setfillcolor(RGB(100, 149, 237)); // 蓝色进度
        fillrectangle(progress_bar_x, progress_bar_y, progress_bar_x + (int)(progress_bar_width * progress), progress_bar_y + progress_bar_height);
    }

    // 绘制进度条边框
    setlinecolor(RGB(255, 215, 0));
    rectangle(progress_bar_x, progress_bar_y, progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 显示进度百分比
    settextcolor(WHITE);
    settextstyle(14, 0, _T("宋体"));
    char progress_text[30];
    snprintf(progress_text, sizeof(progress_text), "%.0f%%", progress * 100);
    outtextxy(progress_bar_x + progress_bar_width + 5, progress_bar_y - 2, progress_text);

    // 显示升级加成信息（紧凑显示）
    settextcolor(RGB(0, 255, 255)); // 青色文字
    settextstyle(10, 0, _T("宋体")); // 更小的字体
    char upgrade_text[80];
    int upgrade_y = progress_bar_y + 15; // 减少间距

    // 子弹速度和伤害
    sprintf(upgrade_text, "子弹: %.1fx速度 %.1fx伤害", upgrade1.bullet_speed_multiplier, upgrade1.bullet_damage_multiplier);
    outtextxy(minimap_x, upgrade_y, upgrade_text);

    // 攻击速度和穿透
    sprintf(upgrade_text, "攻击: %.1fx速度", upgrade1.attack_speed_multiplier);
    if (upgrade1.bullet_penetration > 0) {
        char penetration_text[20];
        sprintf(penetration_text, " 穿透%d", upgrade1.bullet_penetration);
        strcat(upgrade_text, penetration_text);
    }
    outtextxy(minimap_x, upgrade_y + 12, upgrade_text); // 减少行间距

    // 生命值和移动速度
    if (upgrade1.health_bonus > 0 || upgrade1.movement_speed_multiplier > 1.0f) {
        sprintf(upgrade_text, "额外: ");
        if (upgrade1.health_bonus > 0) {
            char health_text[20];
            sprintf(health_text, "+%d生命 ", upgrade1.health_bonus);
            strcat(upgrade_text, health_text);
        }
        if (upgrade1.movement_speed_multiplier > 1.0f) {
            char speed_text[20];
            sprintf(speed_text, "%.1fx移速", upgrade1.movement_speed_multiplier);
            strcat(upgrade_text, speed_text);
        }
        outtextxy(minimap_x, upgrade_y + 24, upgrade_text); // 减少行间距
    }
}

// 绘制玩家2小地图等级信息
void draw_minimap2_level_info(int minimap_x, int minimap_y, int minimap_size) {
    int info_start_y = minimap_y + minimap_size + 120; // 在键位说明下方开始，留出更多空间
    int line_height = 14; // 减少行高使布局更紧凑

    // 获取升级加成
    Player2Upgrade upgrade2 = get_player2_upgrade();

    // 显示等级信息标题
    settextcolor(RGB(50, 205, 50)); // 绿色标题
    settextstyle(14, 0, _T("宋体"));
    outtextxy(minimap_x, info_start_y, "【等级加成】");

    // 显示等级和击杀进度
    settextcolor(RGB(255, 215, 0)); // 金色文字
    settextstyle(12, 0, _T("宋体"));
    char level_text[50];
    if (player2_level <= MAX_LEVEL) {
        snprintf(level_text, sizeof(level_text), "等级: %d", player2_level);
    } else {
        snprintf(level_text, sizeof(level_text), "等级: MAX");
    }
    outtextxy(minimap_x, info_start_y + line_height, level_text);

    // 显示击杀进度
    settextcolor(RGB(144, 238, 144)); // 浅绿色文字
    char kills_text[50];
    int next_level_kills = get_safe_upgrade_kills(player2_level + 1);
    if (player2_level < MAX_LEVEL) {
        snprintf(kills_text, sizeof(kills_text), "击杀: %d/%d", player2_kills, next_level_kills);
    } else {
        snprintf(kills_text, sizeof(kills_text), "击杀: %d (MAX)", player2_kills);
    }
    outtextxy(minimap_x, info_start_y + line_height * 2, kills_text);

    // 绘制升级进度条
    int progress_bar_width = 180; // 稍微小于小地图宽度
    int progress_bar_height = 6;
    int progress_bar_x = minimap_x;
    int progress_bar_y = info_start_y + line_height * 3;

    // 计算进度（使用安全函数）
    float progress = calculate_safe_progress(player2_kills, player2_level);

    // 绘制进度条背景
    setfillcolor(RGB(50, 50, 50));
    fillrectangle(progress_bar_x, progress_bar_y, progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 绘制进度条前景
    if (progress > 0) {
        setfillcolor(RGB(50, 205, 50)); // 绿色进度
        fillrectangle(progress_bar_x, progress_bar_y, progress_bar_x + (int)(progress_bar_width * progress), progress_bar_y + progress_bar_height);
    }

    // 绘制进度条边框
    setlinecolor(RGB(255, 215, 0));
    rectangle(progress_bar_x, progress_bar_y, progress_bar_x + progress_bar_width, progress_bar_y + progress_bar_height);

    // 显示进度百分比
    settextcolor(WHITE);
    settextstyle(14, 0, _T("宋体"));
    char progress_text[30];
    snprintf(progress_text, sizeof(progress_text), "%.0f%%", progress * 100);
    outtextxy(progress_bar_x + progress_bar_width + 5, progress_bar_y - 2, progress_text);

    // 显示升级加成信息（紧凑显示）
    settextcolor(RGB(255, 165, 0)); // 橙色文字
    settextstyle(10, 0, _T("宋体")); // 更小的字体
    char upgrade_text[80];
    int upgrade_y = progress_bar_y + 15; // 减少间距

    // 爆炸范围和伤害
    sprintf(upgrade_text, "爆炸: %dx%d范围 %.1fx伤害", upgrade2.explosion_size, upgrade2.explosion_size, upgrade2.explosion_damage_multiplier);
    outtextxy(minimap_x, upgrade_y, upgrade_text);

    // 炸弹冷却和数量
    sprintf(upgrade_text, "炸弹: %.0f%%冷却 %d个", upgrade2.bomb_cooldown_multiplier * 100, upgrade2.max_bombs);
    outtextxy(minimap_x, upgrade_y + 12, upgrade_text); // 减少行间距

    // 生命值和移动速度
    if (upgrade2.health_bonus > 0 || upgrade2.movement_speed_multiplier > 1.0f) {
        sprintf(upgrade_text, "额外: ");
        if (upgrade2.health_bonus > 0) {
            char health_text[20];
            sprintf(health_text, "+%d生命 ", upgrade2.health_bonus);
            strcat(upgrade_text, health_text);
        }
        if (upgrade2.movement_speed_multiplier > 1.0f) {
            char speed_text[20];
            sprintf(speed_text, "%.1fx移速", upgrade2.movement_speed_multiplier);
            strcat(upgrade_text, speed_text);
        }
        outtextxy(minimap_x, upgrade_y + 24, upgrade_text); // 减少行间距
    }
}

// 安全的内存管理函数
void safe_free_enemies() {
    Enemy* current = enemy_head;
    int count = 0;
    while (current != NULL && count < 1000) { // 防止无限循环
        Enemy* temp = current;
        current = current->next;
        free(temp);
        count++;
    }
    enemy_head = NULL;
    enemy_count = 0;
}

void safe_free_bullets() {
    // 释放玩家1子弹
    Bullet* current1 = bullet_head1;
    int count1 = 0;
    while (current1 != NULL && count1 < 1000) { // 防止无限循环
        Bullet* temp = current1;
        current1 = current1->next;
        free(temp);
        count1++;
    }
    bullet_head1 = NULL;

    // 释放玩家2子弹（虽然不再使用，但为了安全起见）
    Bullet* current2 = bullet_head2;
    int count2 = 0;
    while (current2 != NULL && count2 < 1000) { // 防止无限循环
        Bullet* temp = current2;
        current2 = current2->next;
        free(temp);
        count2++;
    }
    bullet_head2 = NULL;

    // 释放敌人子弹
    EnemyBullet* current_enemy = enemy_bullet_head;
    int count_enemy = 0;
    while (current_enemy != NULL && count_enemy < 1000) { // 防止无限循环
        EnemyBullet* temp = current_enemy;
        current_enemy = current_enemy->next;
        free(temp);
        count_enemy++;
    }
    enemy_bullet_head = NULL;
}

void safe_free_bombing_areas() {
    BombingArea* current = bombing_area_head;
    int count = 0;
    while (current != NULL && count < 100) { // 防止无限循环
        BombingArea* temp = current;
        current = current->next;

        // 释放轰炸区域的单元格链表
        BombingCell* cell_current = temp->cells;
        int cell_count = 0;
        while (cell_current != NULL && cell_count < 50) { // 防止无限循环
            BombingCell* cell_temp = cell_current;
            cell_current = cell_current->next;
            free(cell_temp);
            cell_count++;
        }

        // 释放轰炸区域本身
        free(temp);
        count++;
    }
    bombing_area_head = NULL;
}

void safe_free_bombs() {
    Bomb* current = bomb_head;
    int count = 0;
    while (current != NULL && count < 100) { // 防止无限循环
        Bomb* temp = current;
        current = current->next;
        free(temp);
        count++;
    }
    bomb_head = NULL;
}

// 在游戏重启时调用的清理函数
void cleanup_all_memory() {
    printf("开始清理内存...\n");
    safe_free_enemies();
    safe_free_bullets();
    safe_free_bombing_areas();
    safe_free_bombs();
    printf("内存清理完成\n");
}