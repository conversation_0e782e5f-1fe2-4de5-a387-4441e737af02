#include<easyx/easyx.h>
#include<easyx/graphics.h>
#include <conio.h>
#include <string.h>
#include <time.h>
#include<stdio.h>
#include<math.h>
#include<windows.h> // 添加Windows.h头文件
#include <algorithm> // 为了使用std::max

// 游戏常量定义
#define WINDOW_WIDTH 800
#define WINDOW_HEIGHT 600
#define BLOCK_SIZE 40
#define ENEMY_SPEED 3.5  // 原来是2，现在增加到3.5
#define SOLDIER_SPEED 0.5  // 步幅更小
#define BULLET_SPEED 7

#define SAFE_ZONE_RADIUS 2
#define MAP_WIDTH 8000    // 从4000增加到8000
#define MAP_HEIGHT 8000   // 从4000增加到8000
// 颜色定义
#define GRAY RGB(128, 128, 128)
// 添加地图和迷雾系统相关定义
#define MAP_GRID_SIZE BLOCK_SIZE
#define MAP_GRID_WIDTH (MAP_WIDTH / MAP_GRID_SIZE)
#define MAP_GRID_HEIGHT (MAP_HEIGHT / MAP_GRID_SIZE)

// 地图格子类型
#define GRID_EMPTY 0
#define GRID_OBSTACLE 1
#define GRID_ENEMY 2


// 迷雾状态
#define FOG_UNKNOWN 0  // 未探索
#define FOG_EXPLORED 1 // 已探索

// 添加地图和迷雾数组
int map_grid[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 存储地图信息
int fog_of_war[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 存储迷雾状态

// 结构体定义
typedef struct {
	int x, y,site;
} Soldier;

typedef struct Enemy {
	int x, y;
	struct Enemy* next;
} Enemy;

typedef struct Obstacle {
	int x, y;
	struct Obstacle* next;
} Obstacle;


typedef struct Bullet {
	int x, y;
	int dir_x, dir_y;
	int active;
	struct Bullet* next;
} Bullet;
typedef struct BombingArea {
	int x, y;          // 轰炸区域的左上角坐标
	int active;        // 是否激活（1=激活，0=未激活）
	int marked;        // 是否已标记（1=已标记，0=未标记）
	DWORD create_time; // 创建时间
	DWORD mark_time;   // 标记时间（白色显示后的时间）
	struct BombingArea* next;
} BombingArea;
// 添加摄像机相关变量
typedef struct {
	int x, y;  // 摄像机左上角坐标
} Camera;

// 全局变量
Soldier soldier = {WINDOW_WIDTH / 2 - BLOCK_SIZE / 2, WINDOW_HEIGHT - BLOCK_SIZE * 2};
Enemy* enemy_head = NULL;
Obstacle* obstacle_head = NULL;

Bullet* bullet_head = NULL;
BombingArea* bombing_area_head = NULL; // 添加轰炸区域链表头
int game_over = 0;
int level = 1;

int soldier_direction = 2;   // 初始朝下（0:上, 1:右, 2:下, 3:左）
DWORD last_bombing_check_time = 0;    // 上次轰炸检查的时间
Camera camera = {0, 0};  // 初始化摄像机位置
