#include<easyx/easyx.h>
#include<easyx/graphics.h>
#include <conio.h>
#include <string.h>
#include <time.h>
#include<stdio.h>
#include<math.h>
#include<windows.h> // 添加Windows.h头文件

// 游戏常量定义
#define WINDOW_WIDTH 800
#define WINDOW_HEIGHT 600
#define BLOCK_SIZE 40
#define ENEMY_SPEED 3.5
#define SOLDIER_SPEED 0.5
#define BULLET_SPEED 7
#define BOMB_TIMER 10000
#define SAFE_ZONE_RADIUS 2
#define MAP_WIDTH 2000
#define MAP_HEIGHT 2000
#define MAP_GRID_SIZE 40  // 添加地图网格大小定义
#define MAP_GRID_WIDTH (MAP_WIDTH / MAP_GRID_SIZE)  // 添加地图网格宽度定义
#define MAP_GRID_HEIGHT (MAP_HEIGHT / MAP_GRID_SIZE)  // 添加地图网格高度定义

// 颜色定义
#define GRAY RGB(128, 128, 128)
#define DETECTION_RANGE 200

// 地图状态定义
#define GRID_EMPTY 0
#define GRID_OBSTACLE 1

// 雾战争状态定义
#define FOG_UNKNOWN 0
#define FOG_EXPLORED 1
#define FOG_VISIBLE 2

// 结构体定义
typedef struct {
    int x, y, site;
} Soldier;

typedef struct Enemy {
    int x, y;
    struct Enemy* next;
} Enemy;

typedef struct Obstacle {
    int x, y;
    struct Obstacle* next;
} Obstacle;

typedef struct {
    int x, y;
    int placed;
    DWORD place_time;  // 记录放置时间
    int flag = 0;
} Bomb;

typedef struct Bullet {
    int x, y;
    int dir_x, dir_y;
    int active;
    struct Bullet* next;
} Bullet;

typedef struct BombingArea {
    int x, y;          // 轰炸区域的左上角坐标
    int active;        // 是否激活（1=激活，0=未激活）
    int marked;        // 是否已标记（1=已标记，0=未标记）
    DWORD create_time; // 创建时间
    DWORD mark_time;   // 标记时间（白色显示后的时间）
    struct BombingArea* next;
} BombingArea;

typedef struct {
    int x, y;  // 摄像机左上角坐标
} Camera;

// 函数声明
void init_game();
void draw_game();
void spawn_enemy();
void spawn_obstacle(int count);
void update_enemies();
void check_collision();
void handle_input();
void game_over_screen();
void spawn_bomb();
int obstacle_collision(int x, int y);
void fire_bullet(int site);
void update_bullets();
void draw_bullets();
void check_bomb_timer();
void game_loop();
void free_bombing_areas();
void check_bombing_areas();
void spawn_random_bombing_areas(int count);
void spawn_random_bombing_area();
void check_bombing_area();
void draw_bomb_target_indicator();
void update_camera();
int world_to_screen_x(int world_x);
int world_to_screen_y(int world_y);
int screen_to_world_x(int screen_x);
int screen_to_world_y(int screen_y);
void init_map();
void update_visibility();
void draw_minimap();
void show_story();
int can_place_bomb(int x, int y);
void draw_detection_range(int x, int y);

// 全局变量
Soldier soldier = {WINDOW_WIDTH / 2 - BLOCK_SIZE / 2, WINDOW_HEIGHT - BLOCK_SIZE * 2};
Enemy* enemy_head = NULL;
Obstacle* obstacle_head = NULL;
Bomb bomb = {-1, -1, 0, 0};
Bullet* bullet_head = NULL;
BombingArea* bombing_area_head = NULL; // 添加轰炸区域链表头
int game_over = 0;
int level = 1;
DWORD bomb_placed_time = 0;  // 记录炸弹放置时间
int soldier_direction = 2;   // 初始朝下（0:上, 1:右, 2:下, 3:左）
DWORD last_bombing_check_time = 0;    // 上次轰炸检查的时间
Camera camera = {0, 0};  // 初始化摄像机位置

// 新增地图和迷雾状态的二维数组
int map_grid[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 地图网格状态
int fog_of_war[MAP_GRID_WIDTH][MAP_GRID_HEIGHT]; // 雾战争状态

// 函数声明
void init_game();
void draw_game();
void spawn_enemy();
void spawn_obstacle(int count);
void update_enemies();
void check_collision();
void handle_input();
void game_over_screen();
void spawn_bomb();
int obstacle_collision(int x, int y);
void fire_bullet(int site);
void update_bullets();
void draw_bullets();
void check_bomb_timer();
void game_loop();
void free_bombing_areas();
void check_bombing_areas();
void spawn_random_bombing_areas(int count);
void spawn_random_bombing_area();
void check_bombing_area();
void draw_bomb_target_indicator();
void update_camera();
int world_to_screen_x(int world_x);
int world_to_screen_y(int world_y);
int screen_to_world_x(int screen_x);
int screen_to_world_y(int screen_y);
void init_map();
void update_visibility();
void draw_minimap();
void show_story();
int can_place_bomb(int x, int y);

// 初始化游戏
void init_game() {
	initgraph(WINDOW_WIDTH, WINDOW_HEIGHT);
	setbkcolor(BLACK);
	cleardevice();
	settextcolor(WHITE);
	settextstyle(20, 0, _T("宋体"));
	srand((unsigned)time(NULL));
	
	// 启用双缓冲绘图
	BeginBatchDraw();
	
	// 初始化地图和迷雾
	init_map();
}
void spawn_random_bombing_area() {
	// 创建新的轰炸区域
	BombingArea* new_bombing_area = (BombingArea*)malloc(sizeof(BombingArea));
	if (new_bombing_area == NULL) {
		printf("内存分配失败！\n");
		return;
	}
	
	// 随机生成轰炸区域的左上角坐标
	int valid_position = 0;
	while (!valid_position) {
		new_bombing_area->x = (rand() % ((WINDOW_WIDTH - BLOCK_SIZE * 2) / BLOCK_SIZE)) * BLOCK_SIZE;
		new_bombing_area->y = (rand() % ((WINDOW_HEIGHT - BLOCK_SIZE * 2) / BLOCK_SIZE)) * BLOCK_SIZE;
		
		// 确保轰炸区域不与玩家、障碍物或已放置的炸弹重叠
		valid_position = 1; // 假设初始合法
		
		// 检查是否与玩家位置重叠
		if (new_bombing_area->x == soldier.x && new_bombing_area->y == soldier.y) {
			valid_position = 0;
		}
		
		// 检查是否与障碍物重叠
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL && valid_position) {
			if (new_bombing_area->x == obs_current->x && new_bombing_area->y == obs_current->y) {
				valid_position = 0;
				break;
			}
			obs_current = obs_current->next;
		}
		
		// 检查是否与已放置的炸弹重叠
		if (bomb.placed && 
			new_bombing_area->x == bomb.x && 
			new_bombing_area->y == bomb.y) {
			valid_position = 0;
		}
	}
	
	// 激活轰炸区域
	new_bombing_area->active = 1;
	new_bombing_area->marked = 0;
	new_bombing_area->create_time = GetTickCount();
	new_bombing_area->mark_time = 0;
	
	// 添加到链表头部
	new_bombing_area->next = bombing_area_head;
	bombing_area_head = new_bombing_area;
	
	printf("随机轰炸区域已生成！位置：(%d, %d)\n", new_bombing_area->x, new_bombing_area->y);
}
void check_bombing_area() {
	// 遍历所有轰炸区域
	BombingArea* current_area = bombing_area_head;
	BombingArea* prev_area = NULL;
	
	while (current_area != NULL) {
		if (!current_area->active) {
			prev_area = current_area;
			current_area = current_area->next;
			continue;
		}
		
		// 标记轰炸区域为白色（视觉反馈）
		setfillcolor(WHITE);
		fillrectangle(current_area->x, current_area->y, 
			current_area->x + BLOCK_SIZE, current_area->y + BLOCK_SIZE);
		
		// 检查轰炸区域内的敌人
		Enemy* current_enemy = enemy_head;
		Enemy* prev_enemy = NULL;
		while (current_enemy != NULL) {
			if (current_enemy->x >= current_area->x &&
				current_enemy->x < current_area->x + BLOCK_SIZE &&
				current_enemy->y >= current_area->y &&
				current_enemy->y < current_area->y + BLOCK_SIZE) {
				
				// 移除敌人
				if (prev_enemy == NULL) {
					enemy_head = current_enemy->next;
				} else {
					prev_enemy->next = current_enemy->next;
				}
				Enemy* temp = current_enemy;
				current_enemy = current_enemy->next;
				free(temp);
				printf("敌人在轰炸中被消灭！\n");
				continue;
			}
			prev_enemy = current_enemy;
			current_enemy = current_enemy->next;
		}
		
		// 检查轰炸区域内的士兵
		if (soldier.x >= current_area->x &&
			soldier.x < current_area->x + BLOCK_SIZE &&
			soldier.y >= current_area->y &&
			soldier.y < current_area->y + BLOCK_SIZE) {
			game_over = 1; // 士兵阵亡
			printf("玩家在轰炸中被消灭！\n");
		}
		
		// 检查轰炸区域内的障碍物
		Obstacle* current_obstacle = obstacle_head;
		Obstacle* prev_obstacle = NULL;
		while (current_obstacle != NULL) {
			if (current_obstacle->x >= current_area->x &&
				current_obstacle->x < current_area->x + BLOCK_SIZE &&
				current_obstacle->y >= current_area->y &&
				current_obstacle->y < current_area->y + BLOCK_SIZE) {
				
				// 移除障碍物
				if (prev_obstacle == NULL) {
					obstacle_head = current_obstacle->next;
				} else {
					prev_obstacle->next = current_obstacle->next;
				}
				Obstacle* temp = current_obstacle;
				current_obstacle = current_obstacle->next;
				free(temp);
				printf("障碍物在轰炸中被清除！\n");
				continue;
			}
			prev_obstacle = current_obstacle;
			current_obstacle = current_obstacle->next;
		}
		
		// 移除当前轰炸区域
		if (prev_area == NULL) {
			bombing_area_head = current_area->next;
			free(current_area);
			current_area = bombing_area_head;
		} else {
			prev_area->next = current_area->next;
			free(current_area);
			current_area = prev_area->next;
		}
	}
	
	// 清屏（移除白色标记）
	cleardevice();
}
// 游戏主循环
void game_loop() {
	spawn_bomb();
	while (!game_over) {
		handle_input();
		update_enemies();
		update_bullets();
		check_collision();
		check_bomb_timer();
		
		// 检查轰炸区域
		check_bombing_areas();
		
		// 随机生成轰炸区域，炸弹放置前生成概率更高
		DWORD current_time = GetTickCount();
		if (!bomb.placed && (current_time - last_bombing_check_time >= 1500)) {  // 从3000ms减少到1500ms
			// 炸弹未放置时，生成更多轰炸区域
			spawn_random_bombing_areas(rand() % 4 + 2);  // 从(rand() % 3 + 1)改为(rand() % 4 + 2)
			last_bombing_check_time = current_time;
		} else if (bomb.placed && (current_time - last_bombing_check_time >= 4000)) {
			// 炸弹已放置时，减少轰炸区域生成
			spawn_random_bombing_areas(rand() % 2 + 1);
			last_bombing_check_time = current_time;
		}
		
		draw_game();
		FlushBatchDraw(); // 刷新绘图缓冲区
		Sleep(50); // 控制帧率
		
		// 随机生成敌人
		static int enemy_spawn_timer = 0;
		enemy_spawn_timer++;
		if (enemy_spawn_timer >= 60) {  // 每60帧生成一个敌人
			spawn_enemy();
			enemy_spawn_timer = 0;
		}
	}
	
	EndBatchDraw(); // 结束批量绘图
	game_over_screen();
}
void spawn_obstacle(int count) {
	for (int i = 0; i < count; i++) {
		Obstacle* new_obstacle = (Obstacle*)malloc(sizeof(Obstacle));
		if (new_obstacle == NULL) {
			printf("内存分配失败！\n");
			exit(1);
		}
		
		// 随机生成障碍物位置（确保不超出屏幕边界）
		new_obstacle->x = (rand() % ((WINDOW_WIDTH - BLOCK_SIZE) / BLOCK_SIZE)) * BLOCK_SIZE;
		new_obstacle->y = (rand() % ((WINDOW_HEIGHT - BLOCK_SIZE * 2) / BLOCK_SIZE)) * BLOCK_SIZE;
		
		// 确保障碍物不会生成在玩家初始位置
		while ((abs(new_obstacle->x - soldier.x) < BLOCK_SIZE && abs(new_obstacle->y - soldier.y) < BLOCK_SIZE)) {
			new_obstacle->x = (rand() % ((WINDOW_WIDTH - BLOCK_SIZE) / BLOCK_SIZE)) * BLOCK_SIZE;
			new_obstacle->y = (rand() % ((WINDOW_HEIGHT - BLOCK_SIZE * 2) / BLOCK_SIZE)) * BLOCK_SIZE;
		}
		
		// 将新障碍物添加到链表头部
		new_obstacle->next = obstacle_head;
		obstacle_head = new_obstacle;
	}
}
// 绘制游戏画面
void draw_game() {
	cleardevice();
	
	// 更新摄像机位置
	update_camera();
	
	// 更新可见性
	update_visibility();
	
	// 绘制地图格子
	for (int x = 0; x < MAP_GRID_WIDTH; x++) {
		for (int y = 0; y < MAP_GRID_HEIGHT; y++) {
			int screen_x = world_to_screen_x(x * MAP_GRID_SIZE);
			int screen_y = world_to_screen_y(y * MAP_GRID_SIZE);
			
			// 如果格子在屏幕外，跳过
			if (screen_x < -MAP_GRID_SIZE || screen_x > WINDOW_WIDTH ||
				screen_y < -MAP_GRID_SIZE || screen_y > WINDOW_HEIGHT) {
				continue;
			}
			
			// 根据迷雾状态绘制
			if (fog_of_war[x][y] == FOG_UNKNOWN) {
				// 未探索区域绘制为黑色
				setfillcolor(BLACK);
				fillrectangle(screen_x, screen_y, 
					screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
			} else if (fog_of_war[x][y] == FOG_EXPLORED) {
				// 已探索但不可见区域绘制为深灰色
				setfillcolor(RGB(50, 50, 50));
				fillrectangle(screen_x, screen_y, 
					screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
				
				// 绘制已知的障碍物
				if (map_grid[x][y] == GRID_OBSTACLE) {
					setfillcolor(GRAY);
					fillrectangle(screen_x, screen_y, 
						screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
				}
			} else if (fog_of_war[x][y] == FOG_VISIBLE) {
				// 可见区域绘制为浅灰色背景
				setfillcolor(RGB(200, 200, 200));
				fillrectangle(screen_x, screen_y, 
					screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
				
				// 绘制可见区域的内容
				if (map_grid[x][y] == GRID_OBSTACLE) {
					setfillcolor(GRAY);
					fillrectangle(screen_x, screen_y, 
						screen_x + MAP_GRID_SIZE, screen_y + MAP_GRID_SIZE);
				}
			}
		}
	}
	
	// 绘制玩家（蓝色方块）
	setfillcolor(BLUE);
	fillrectangle(world_to_screen_x(soldier.x), world_to_screen_y(soldier.y), 
		world_to_screen_x(soldier.x + BLOCK_SIZE), world_to_screen_y(soldier.y + BLOCK_SIZE));
	
	// 绘制可见区域内的敌人（红色方块）
	Enemy* current = enemy_head;
	while (current != NULL) {
		int enemy_grid_x = current->x / MAP_GRID_SIZE;
		int enemy_grid_y = current->y / MAP_GRID_SIZE;
		
		if (enemy_grid_x >= 0 && enemy_grid_x < MAP_GRID_WIDTH && 
			enemy_grid_y >= 0 && enemy_grid_y < MAP_GRID_HEIGHT &&
			fog_of_war[enemy_grid_x][enemy_grid_y] == FOG_VISIBLE) {
			
			setfillcolor(RED);
			fillrectangle(world_to_screen_x(current->x), world_to_screen_y(current->y), 
				world_to_screen_x(current->x + BLOCK_SIZE), world_to_screen_y(current->y + BLOCK_SIZE));
		}
		current = current->next;
	}
	
	// 绘制炸弹位置（绿色方块），如果已放置且在可见区域
	if (bomb.placed) {
		int bomb_grid_x = bomb.x / MAP_GRID_SIZE;
		int bomb_grid_y = bomb.y / MAP_GRID_SIZE;
		
		if (bomb_grid_x >= 0 && bomb_grid_x < MAP_GRID_WIDTH && 
			bomb_grid_y >= 0 && bomb_grid_y < MAP_GRID_HEIGHT &&
			(fog_of_war[bomb_grid_x][bomb_grid_y] == FOG_VISIBLE || 
				fog_of_war[bomb_grid_x][bomb_grid_y] == FOG_EXPLORED)) {
			
			setfillcolor(GREEN);
			fillrectangle(world_to_screen_x(bomb.x), world_to_screen_y(bomb.y), 
				world_to_screen_x(bomb.x + BLOCK_SIZE), world_to_screen_y(bomb.y + BLOCK_SIZE));
		}
	}
	
	// 绘制小地图（右上角）
	draw_minimap();
}
void spawn_bomb() {
	// 随机生成炸弹位置（确保在合法范围内）
	int valid_position = 0;
	while (!valid_position) {
		bomb.x = (rand() % ((WINDOW_WIDTH - BLOCK_SIZE*2) / BLOCK_SIZE)) * BLOCK_SIZE;
		bomb.y = (rand() % ((WINDOW_HEIGHT - BLOCK_SIZE * 3) / BLOCK_SIZE)) * BLOCK_SIZE;
		
		// 检查位置是否合法（非障碍物、非玩家位置）
		valid_position = 1; // 假设初始合法
		
		// 检查是否与障碍物重叠
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL) {
			if (bomb.x == obs_current->x && bomb.y == obs_current->y) {
				valid_position = 0;
				break;
			}
			obs_current = obs_current->next;
		}
		
		// 检查是否与玩家位置重叠
		if (bomb.x == soldier.x && bomb.y == soldier.y) {
			valid_position = 0;
		}
	}
	// 设置炸弹状态为未放置
	bomb.placed = 0;  // 关键修正：炸弹生成后默认未放置
	bomb_placed_time = 0;  // 清除之前的放置时间
	printf("新炸弹已生成！位置：(%d, %d)，等待玩家放置。\n", bomb.x, bomb.y);
}
void draw_bomb_target_indicator() {
	if (!bomb.placed && bomb_placed_time == 0) {
		// 计算玩家到炸弹的方向
		int dx = bomb.x - soldier.x;
		int dy = bomb.y - soldier.y;
		double distance = sqrt(dx*dx + dy*dy);
		
		// 如果炸弹不在屏幕上，绘制指向箭头
		if (distance > WINDOW_WIDTH / 3) {
			// 标准化方向向量
			double nx = dx / distance;
			double ny = dy / distance;
			
			// 计算箭头位置（在玩家周围一定距离）
			int arrow_dist = 100;
			int arrow_x = WINDOW_WIDTH / 2 + (int)(nx * arrow_dist);
			int arrow_y = WINDOW_HEIGHT / 2 + (int)(ny * arrow_dist);
			
			// 绘制箭头
			setlinecolor(YELLOW);
			setfillcolor(YELLOW);
			
			// 箭头主体
			line(WINDOW_WIDTH / 2, WINDOW_HEIGHT / 2, arrow_x, arrow_y);
			
			// 箭头头部
			double angle = atan2(ny, nx);
			int arrow_size = 10;
			int x1 = arrow_x - (int)(arrow_size * cos(angle - 0.5));
			int y1 = arrow_y - (int)(arrow_size * sin(angle - 0.5));
			int x2 = arrow_x - (int)(arrow_size * cos(angle + 0.5));
			int y2 = arrow_y - (int)(arrow_size * sin(angle + 0.5));
			
			POINT pts[] = {{arrow_x, arrow_y}, {x1, y1}, {x2, y2}};
			fillpolygon(pts, 3);
			
			// 显示距离信息
			char dist_text[20];
			sprintf(dist_text, "%.0f", distance / BLOCK_SIZE);
			settextcolor(YELLOW);
			outtextxy(arrow_x + 10, arrow_y, dist_text);
		}
	}
}
void spawn_enemy() {
	int valid_position = 0;
	while (!valid_position) {
		// 随机生成敌人位置
		Enemy* new_enemy = (Enemy*)malloc(sizeof(Enemy));
		if (new_enemy == NULL) {
			printf("内存分配失败！\n");
			exit(1);
		}
		
		new_enemy->x = (rand() % ((WINDOW_WIDTH - BLOCK_SIZE) / BLOCK_SIZE)) * BLOCK_SIZE;
		new_enemy->y = (rand() % ((WINDOW_HEIGHT - BLOCK_SIZE * 3) / BLOCK_SIZE)) * BLOCK_SIZE;
		
		// 确保敌人不会生成在玩家初始位置、障碍物或炸弹位置
		valid_position = 1; // 假设初始合法
		
		// 检查是否与玩家位置重叠
		if (new_enemy->x == soldier.x && new_enemy->y == soldier.y) {
			valid_position = 0;
			free(new_enemy);
			continue;
		}
		
		// 检查是否与障碍物重叠
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL) {
			if (new_enemy->x == obs_current->x && new_enemy->y == obs_current->y) {
				valid_position = 0;
				break;
			}
			obs_current = obs_current->next;
		}
		
		// 检查是否与炸弹位置重叠
		if (bomb.placed) {
			if (new_enemy->x == bomb.x && new_enemy->y == bomb.y) {
				valid_position = 0;
			}
		}
		
		// 如果位置合法，将敌人添加到链表头部
		if (valid_position) {
			new_enemy->next = enemy_head;
			enemy_head = new_enemy;
			printf("敌人在 (%d, %d) 生成\n", new_enemy->x, new_enemy->y);
		} else {
			free(new_enemy);
		}
	}
}
// 更新子弹位置并检测碰撞
void update_bullets() {
	Bullet* current_bullet = bullet_head;
	Bullet* prev_bullet = NULL;
	
	while (current_bullet != NULL) {
		if (current_bullet->active) {
			// 更新子弹位置
			current_bullet->x += current_bullet->dir_x;
			current_bullet->y += current_bullet->dir_y;
			
			// 边界检查
			if (current_bullet->x < 0 || current_bullet->x > WINDOW_WIDTH - BLOCK_SIZE ||
				current_bullet->y < 0 || current_bullet->y > WINDOW_HEIGHT - BLOCK_SIZE) {
				current_bullet->active = 0;
			}
			
			// 障碍物碰撞检测
			Obstacle* obs_current = obstacle_head;
			int hit_obstacle = 0;
			while (obs_current != NULL && !hit_obstacle) {
				if (current_bullet->x < obs_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE > obs_current->x &&
					current_bullet->y < obs_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE > obs_current->y) {
					hit_obstacle = 1;
				}
				obs_current = obs_current->next;
			}
			if (hit_obstacle) {
				current_bullet->active = 0;
			}
			
			// 敌人碰撞检测
			Enemy* enemy_current = enemy_head;
			Enemy* prev_enemy = NULL;
			int hit_enemy = 0;
			
			while (enemy_current != NULL && !hit_enemy) {
				if (current_bullet->x < enemy_current->x + BLOCK_SIZE &&
					current_bullet->x + BLOCK_SIZE > enemy_current->x &&
					current_bullet->y < enemy_current->y + BLOCK_SIZE &&
					current_bullet->y + BLOCK_SIZE > enemy_current->y) {
					// 子弹击中敌人
					printf("敌人被击中！\n");
					
					// 移除敌人节点
					if (prev_enemy == NULL) {
						// 当前敌人是头节点
						enemy_head = enemy_current->next;
					} else {
						prev_enemy->next = enemy_current->next;
					}
					Enemy* temp_enemy = enemy_current;
					enemy_current = enemy_current->next;
					free(temp_enemy);
					
					// 移除子弹节点
					if (prev_bullet == NULL) {
						// 当前子弹是头节点
						bullet_head = current_bullet->next;
					} else {
						prev_bullet->next = current_bullet->next;
					}
					Bullet* temp_bullet = current_bullet;
					current_bullet = current_bullet->next;
					free(temp_bullet);
					
					hit_enemy = 1; // 标记已处理
				} else {
					prev_enemy = enemy_current;
					enemy_current = enemy_current->next;
				}
			}
			
			if (hit_enemy) {
				continue; // 跳过后续逻辑
			}
			
			prev_bullet = current_bullet;
		}
		current_bullet = current_bullet->next;
	}
	
	
}
// 绘制子弹
void draw_bullets() {
	Bullet* current = bullet_head;
	while (current != NULL) {
		if (current->active) {
			setfillcolor(WHITE);
			fillcircle(current->x + BLOCK_SIZE / 2, current->y + BLOCK_SIZE / 2, BLOCK_SIZE / 4);
			// 调试信息：打印子弹位置
			printf("绘制子弹：位置 (%d, %d)\n", current->x + BLOCK_SIZE / 2, current->y + BLOCK_SIZE / 2);
		}
		current = current->next;
	}
}
// 检查炸弹计时器
void check_bomb_timer() {
	if (bomb.placed && GetTickCount() - bomb_placed_time >= BOMB_TIMER) {
		printf("炸弹爆炸！关卡胜利！\n");
		level++;
		
		// 重置游戏状态
		Enemy* current = enemy_head;
		while (current != NULL) {
			Enemy* temp = current;
			current = current->next;
			free(temp);
		}
		enemy_head = NULL;
		
		Obstacle* obs_current = obstacle_head;
		while (obs_current != NULL) {
			Obstacle* temp = obs_current;
			obs_current = obs_current->next;
			free(temp);
		}
		obstacle_head = NULL;
		
		// 重新生成障碍物
		spawn_obstacle(10);
		
		// 重置炸弹状态
		bomb.placed = 0;
		bomb_placed_time = 0;
	}
}
// 处理输入
void handle_input() {
	int can_place_bomb(int x, int y);
	// 检查是否有按键消息
	ExMessage msg;
	while (peekmessage(&msg, EM_KEY)) {
		if (msg.message == WM_KEYDOWN) {
			int key = msg.vkcode;
			
			// 士兵移动逻辑
			if (key == 'W' || key == VK_UP) {
				// 朝上移动
				int new_x = soldier.x;
				int new_y = soldier.y - SOLDIER_SPEED * BLOCK_SIZE;
				soldier_direction = 0;  // 更新朝向为上
				soldier.site = 1;       // 更新朝向标记为上
				
				// 边界检查和障碍物碰撞检测
				if (new_y >= 0) { // 边界检查
					// 障碍物碰撞检测
					Obstacle* obs_current = obstacle_head;
					int blocked = 0;
					while (obs_current != NULL && !blocked) {
						if (new_x < obs_current->x + BLOCK_SIZE &&
							new_x + BLOCK_SIZE > obs_current->x &&
							new_y < obs_current->y + BLOCK_SIZE &&
							new_y + BLOCK_SIZE > obs_current->y) {
							blocked = 1;
						}
						obs_current = obs_current->next;
					}
					if (!blocked) {
						soldier.y = new_y;
					}
				}
			} else if (key == 'S' || key == VK_DOWN) {
				// 朝下移动
				int new_x = soldier.x;
				int new_y = soldier.y + SOLDIER_SPEED * BLOCK_SIZE;
				soldier_direction = 2;  // 更新朝向为下
				soldier.site = 3;       // 更新朝向标记为下
				
				// 边界检查和障碍物碰撞检测
				if (new_y <= WINDOW_HEIGHT - BLOCK_SIZE) { // 边界检查
					// 障碍物碰撞检测
					Obstacle* obs_current = obstacle_head;
					int blocked = 0;
					while (obs_current != NULL && !blocked) {
						if (new_x < obs_current->x + BLOCK_SIZE &&
							new_x + BLOCK_SIZE > obs_current->x &&
							new_y < obs_current->y + BLOCK_SIZE &&
							new_y + BLOCK_SIZE > obs_current->y) {
							blocked = 1;
						}
						obs_current = obs_current->next;
					}
					if (!blocked) {
						soldier.y = new_y;
					}
				}
			} else if (key == 'A' || key == VK_LEFT) {
				// 朝左移动
				int new_x = soldier.x - SOLDIER_SPEED * BLOCK_SIZE;
				int new_y = soldier.y;
				soldier_direction = 3;  // 更新朝向为左
				soldier.site = 4;       // 更新朝向标记为左
				
				// 边界检查和障碍物碰撞检测
				if (new_x >= 0) { // 边界检查
					// 障碍物碰撞检测
					Obstacle* obs_current = obstacle_head;
					int blocked = 0;
					while (obs_current != NULL && !blocked) {
						if (new_x < obs_current->x + BLOCK_SIZE &&
							new_x + BLOCK_SIZE > obs_current->x &&
							new_y < obs_current->y + BLOCK_SIZE &&
							new_y + BLOCK_SIZE > obs_current->y) {
							blocked = 1;
						}
						obs_current = obs_current->next;
					}
					if (!blocked) {
						soldier.x = new_x;
					}
				}
			} else if (key == 'D' || key == VK_RIGHT) {
				// 朝右移动
				int new_x = soldier.x + SOLDIER_SPEED * BLOCK_SIZE;
				int new_y = soldier.y;
				soldier_direction = 1;  // 更新朝向为右
				soldier.site = 2;       // 更新朝向标记为右
				
				// 边界检查和障碍物碰撞检测
				if (new_x <= WINDOW_WIDTH - BLOCK_SIZE) { // 边界检查
					// 障碍物碰撞检测
					Obstacle* obs_current = obstacle_head;
					int blocked = 0;
					while (obs_current != NULL && !blocked) {
						if (new_x < obs_current->x + BLOCK_SIZE &&
							new_x + BLOCK_SIZE > obs_current->x &&
							new_y < obs_current->y + BLOCK_SIZE &&
							new_y + BLOCK_SIZE > obs_current->y) {
							blocked = 1;
						}
						obs_current = obs_current->next;
					}
					if (!blocked) {
						soldier.x = new_x;
					}
				}
			}
			
			// 放置炸弹逻辑
			if (key == 'J') {
				if (!bomb.placed && bomb_placed_time == 0) {
					// 检查士兵当前位置是否在黄色区域内
					if (can_place_bomb(soldier.x, soldier.y)) {
						// 放置炸弹
						bomb.placed = 1;
						bomb_placed_time = GetTickCount();
						printf("炸弹已放置在 (%d, %d)！\n", bomb.x, bomb.y);
					} else {
						printf("无法在此位置放置炸弹！请移动到黄色区域。\n");
					}
				}
			}
			
			// 发射子弹逻辑
			if (key == 'K') {
				if (bomb.placed) {
					// 根据士兵的朝向发射子弹
					fire_bullet(soldier.site);
				} else {
					printf("炸弹未放置，无法发射子弹！\n");
				}
			}
			
			// 添加侦测仪功能
			if (key == 'E') {
				// 绘制侦测范围
				draw_detection_range(soldier.x + BLOCK_SIZE / 2, soldier.y + BLOCK_SIZE / 2);

				// 显示侦测范围内的敌人
				Enemy* current = enemy_head;
				while (current != NULL) {
					int dx = current->x - soldier.x;
					int dy = current->y - soldier.y;
					double distance = sqrt(dx * dx + dy * dy);

					if (distance <= DETECTION_RANGE) {
						// 绘制敌人的位置（红色标注）
						setfillcolor(RGB(255, 0, 0));
						fillrectangle(world_to_screen_x(current->x), world_to_screen_y(current->y),
									  world_to_screen_x(current->x + BLOCK_SIZE), world_to_screen_y(current->y + BLOCK_SIZE));
					}

					current = current->next;
				}
			}
		}
	}
}

// 检查是否可以放置炸弹
int can_place_bomb(int x, int y) {
	// 检查是否与障碍物重叠
	if(x>=bomb.x-BLOCK_SIZE&&x<=bomb.x+BLOCK_SIZE&&y>=bomb.y-BLOCK_SIZE&&y<=bomb.y+BLOCK_SIZE){
		return 1;
	}
	else{
		return 0;
	} 
}

// 发射子弹
void fire_bullet(int site) {
	Bullet* new_bullet = (Bullet*)malloc(sizeof(Bullet));
	if (!new_bullet) {
		printf("内存分配失败！\n");
		return;
	}
	
	// 设置子弹初始位置（从士兵中心发射）
	new_bullet->x = soldier.x + BLOCK_SIZE / 2 - BLOCK_SIZE / 2;
	new_bullet->y = soldier.y + BLOCK_SIZE / 2 - BLOCK_SIZE / 2;
	
	// 根据site设置方向
	switch (soldier_direction) {
		case 0: // 上
		new_bullet->dir_x = 0;
		new_bullet->dir_y = -BULLET_SPEED;
		break;
		case 1: // 右
		new_bullet->dir_x = BULLET_SPEED;
		new_bullet->dir_y = 0;
		break;
		case 2: // 下
		new_bullet->dir_x = 0;
		new_bullet->dir_y = BULLET_SPEED;
		break;
		case 3: // 左
		new_bullet->dir_x = -BULLET_SPEED;
		new_bullet->dir_y = 0;
		break;
	default:
		printf("无效的朝向！\n");
		free(new_bullet);
		return;
	}
	
	new_bullet->active = 1;
	new_bullet->next = bullet_head;
	bullet_head = new_bullet;
	printf("子弹已发射，方向：%d\n", site);
	
	// 在玩家位置创建轰炸区域
	BombingArea* new_area = (BombingArea*)malloc(sizeof(BombingArea));
	if (new_area == NULL) {
		printf("内存分配失败！\n");
		return;
	}
	
	new_area->x = soldier.x;
	new_area->y = soldier.y;
	new_area->active = 1;
	new_area->marked = 0;
	new_area->create_time = GetTickCount();
	new_area->mark_time = 0;
	new_area->next = bombing_area_head;
	bombing_area_head = new_area;
	
	printf("玩家位置轰炸区域已生成！位置：(%d, %d)\n", new_area->x, new_area->y);
}
// 更新子弹位置并检测碰撞


// 绘制子弹

// 检查炸弹计时器

// 更新敌人移动逻辑
void update_enemies() {
	Enemy* current = enemy_head;
	int target_x,target_y;
	while (current != NULL) {
		// 炸弹放置后，敌人朝炸弹移动
		if (bomb.placed) {
			target_x = bomb.x + BLOCK_SIZE / 2;
			target_y = bomb.y + BLOCK_SIZE / 2;
		} else {
			// 炸弹未放置时，敌人朝士兵移动
			target_x = soldier.x + BLOCK_SIZE / 2;
			target_y = soldier.y + BLOCK_SIZE / 2;
		}
		
		// 计算方向向量
		int dx = target_x - (current->x + BLOCK_SIZE / 2);
		int dy = target_y - (current->y + BLOCK_SIZE / 2);
		double dist = sqrt(dx * dx + dy * dy);
		if (dist > 0) {
			dx = (int)((double)dx / dist * ENEMY_SPEED);
			dy = (int)((double)dy / dist * ENEMY_SPEED);
		}
		
		// 更新敌人位置
		int new_x = current->x + dx;
		int new_y = current->y + dy;
		
		// 边界检查
		if (new_x < 0) new_x = 0;
		if (new_x > WINDOW_WIDTH - BLOCK_SIZE) new_x = WINDOW_WIDTH - BLOCK_SIZE;
		if (new_y < 0) new_y = 0;
		if (new_y > WINDOW_HEIGHT - BLOCK_SIZE) new_y = WINDOW_HEIGHT - BLOCK_SIZE;
		
		// 忽略障碍物碰撞（直接穿越）
		current->x = new_x;
		current->y = new_y;
		
		current = current->next;
	}
}

// 碰撞检测
void check_collision() {
	// 检测士兵与敌人的碰撞
	Enemy* current = enemy_head;
	while (current != NULL) {
		if (soldier.x < current->x + BLOCK_SIZE &&
			soldier.x + BLOCK_SIZE > current->x &&
			soldier.y < current->y + BLOCK_SIZE &&
			soldier.y + BLOCK_SIZE > current->y) {
			game_over = 1; // 游戏结束
			return;
		}
		current = current->next;
	}
	
	// 检测敌人是否碰到炸弹
	if (bomb.placed) {
		current = enemy_head;
		while (current != NULL) {
			if (current->x < bomb.x + BLOCK_SIZE &&
				current->x + BLOCK_SIZE > bomb.x &&
				current->y < bomb.y + BLOCK_SIZE &&
				current->y + BLOCK_SIZE > bomb.y) {
				game_over=1;
				printf("敌人拆除了炸弹");
				// 这里可以添加额外逻辑，比如敌人被炸飞等
				break;
			}
			current = current->next;
		}
	}
}

// 游戏主循环


// 游戏结束屏幕
void game_over_screen() {
	cleardevice();
	settextcolor(RED);
	settextstyle(30, 0, _T("宋体"));
	outtextxy(WINDOW_WIDTH / 2 - 100, WINDOW_HEIGHT / 2 - 50, "游戏结束！");
	settextstyle(20, 0, _T("宋体"));
	outtextxy(WINDOW_WIDTH / 2 - 120, WINDOW_HEIGHT / 2, "按任意键退出游戏...");
	FlushBatchDraw();
	
	// 释放内存
	free_bombing_areas();
	
	// 使用EasyX消息机制等待用户按键
	bool key_pressed = false;
	ExMessage msg;
	while (!key_pressed) {
		// 检查是否有按键消息
		if (peekmessage(&msg, EM_KEY | EM_MOUSE)) {
			if (msg.message == WM_KEYDOWN || msg.message == WM_LBUTTONDOWN) {
				key_pressed = true;
			}
		}
		Sleep(10); // 短暂休眠，减少CPU占用
	}
	
	closegraph();
}

// 在游戏结束时释放轰炸区域内存
void free_bombing_areas() {
	BombingArea* current = bombing_area_head;
	while (current != NULL) {
		BombingArea* temp = current;
		current = current->next;
		free(temp);
	}
	bombing_area_head = NULL;
}

// 主函数
int main() {
	// 隐藏控制台窗口
	HWND hwnd = GetConsoleWindow();
	ShowWindow(hwnd, SW_HIDE);
	void show_story();
	// 初始化游戏
	init_game();
	
	// 显示背景故事
	show_story();
	
	// 开始游戏
	spawn_obstacle(10);  // 初始生成10个障碍物
	game_loop();
	
	return 0;
}

// 实现 check_bombing_areas 函数
void check_bombing_areas() {
	DWORD current_time = GetTickCount();
	BombingArea* current = bombing_area_head;
	BombingArea* prev = NULL;
	
	while (current != NULL) {
		// 检查轰炸区域是否激活
		if (current->active) {
			// 如果未标记，先标记为白色（轰炸警告）
			if (!current->marked) {
				current->marked = 1;
				current->mark_time = current_time;
			}
			// 如果已标记，检查是否到达轰炸时间（2秒后）
			else if (current_time - current->mark_time >= 2000) {
				// 执行轰炸判定
				
				// 检查轰炸区域内的敌人
				Enemy* current_enemy = enemy_head;
				Enemy* prev_enemy = NULL;
				while (current_enemy != NULL) {
					if (current_enemy->x >= current->x &&
						current_enemy->x < current->x + BLOCK_SIZE &&
						current_enemy->y >= current->y &&
						current_enemy->y < current->y + BLOCK_SIZE) {
						
						// 移除敌人
						if (prev_enemy == NULL) {
							enemy_head = current_enemy->next;
						} else {
							prev_enemy->next = current_enemy->next;
						}
						Enemy* temp = current_enemy;
						current_enemy = current_enemy->next;
						free(temp);
						printf("敌人在轰炸中被消灭！\n");
						continue;
					}
					prev_enemy = current_enemy;
					current_enemy = current_enemy->next;
				}
				
				// 检查轰炸区域内的士兵
				if (soldier.x >= current->x &&
					soldier.x < current->x + BLOCK_SIZE &&
					soldier.y >= current->y &&
					soldier.y < current->y + BLOCK_SIZE) {
					game_over = 1; // 士兵阵亡
					printf("玩家在轰炸中被消灭！\n");
				}
				
				// 检查轰炸区域内的障碍物
				Obstacle* current_obstacle = obstacle_head;
				Obstacle* prev_obstacle = NULL;
				while (current_obstacle != NULL) {
					if (current_obstacle->x >= current->x &&
						current_obstacle->x < current->x + BLOCK_SIZE &&
						current_obstacle->y >= current->y &&
						current_obstacle->y < current->y + BLOCK_SIZE) {
						
						// 移除障碍物
						if (prev_obstacle == NULL) {
							obstacle_head = current_obstacle->next;
						} else {
							prev_obstacle->next = current_obstacle->next;
						}
						Obstacle* temp = current_obstacle;
						current_obstacle = current_obstacle->next;
						free(temp);
						printf("障碍物在轰炸中被清除！\n");
						continue;
					}
					prev_obstacle = current_obstacle;
					current_obstacle = current_obstacle->next;
				}
				
				// 轰炸完成后，将区域变为紫色（保持一段时间）
				current->active = 0; // 不再激活轰炸判定
			}
		}
		
		// 检查紫色区域是否需要清除（轰炸后5秒）
		if (!current->active && current->marked && 
			current_time - current->mark_time >= 5000) {
			// 移除轰炸区域
			if (prev == NULL) {
				bombing_area_head = current->next;
				free(current);
				current = bombing_area_head;
			} else {
				prev->next = current->next;
				free(current);
				current = prev->next;
			}
		} else {
			prev = current;
			current = current->next;
		}
	}
	
	// 移除了这里的 cleardevice() 调用
}

// 实现 spawn_random_bombing_areas 函数
void spawn_random_bombing_areas(int count) {
	// 计算当前轰炸区数量
	int current_count = 0;
	BombingArea* current = bombing_area_head;
	while (current != NULL) {
		current_count++;
		current = current->next;
	}
	
	// 限制最大轰炸区域数量为10个
	int max_areas = 10;
	if (current_count >= max_areas) {
		return;  // 如果已经达到最大数量，不再生成
	}
	
	// 计算实际需要生成的数量
	int to_spawn = (current_count + count > max_areas) ? (max_areas - current_count) : count;
	
	// 生成新的轰炸区域
	for (int i = 0; i < to_spawn; i++) {
		spawn_random_bombing_area();
	}
}

// 添加显示游戏背景故事的函数
void show_story() {
	// 设置黑色背景
	setbkcolor(BLACK);
	cleardevice();
	
	// 设置文字样式
	settextcolor(WHITE);
	settextstyle(24, 0, _T("宋体"));
	
	// 背景故事文本
	const char* story_lines[] = {
		"百团大战背景故事",
		"",
		"1940年8月，为了粉碎日军的'囚笼政策'，",
		"八路军总部决定在华北发动大规模攻势，",
		"这就是著名的百团大战。",
		"",
		"作为八路军的一名战士，",
		"你的任务是潜入敌后，",
		"安置炸弹破坏日军控制的铁路和桥梁，",
		"切断日军的补给线。",
		"",
		"小心日军巡逻，避开轰炸区，",
		"成功安放炸弹并保护它直到爆炸。",
		"",
		"祝你好运，勇士！",
		"",
		"按任意键开始游戏..."
	};
	
	// 逐行显示文本
	const int line_count = sizeof(story_lines) / sizeof(story_lines[0]);
	const int start_y = 100;
	const int line_height = 30;
	
	for (int i = 0; i < line_count; i++) {
		// 计算当前行的Y坐标
		int y = start_y + i * line_height;
		
		// 逐字显示当前行文本
		const char* line = story_lines[i];
		int len = strlen(line);
		char buffer[256] = {0};
		
		for (int j = 0; j < len; j++) {
			// 复制部分字符串
			strncpy(buffer, line, j + 1);
			buffer[j + 1] = '\0';
			
			// 清除当前行
			setfillcolor(BLACK);
			solidrectangle(0, y, WINDOW_WIDTH, y + line_height);
			
			// 显示文本
			outtextxy(WINDOW_WIDTH / 2 - textwidth(line) / 2, y, buffer);
			
			// 延时，控制显示速度
			Sleep(30);
			FlushBatchDraw();
		}
		
		// 每行显示完后稍作停顿
		Sleep(200);
	}
	
	// 使用EasyX消息机制等待用户按键
	bool key_pressed = false;
	ExMessage msg;
	while (!key_pressed) {
		// 检查是否有按键消息
		if (peekmessage(&msg, EM_KEY | EM_MOUSE)) {
			if (msg.message == WM_KEYDOWN || msg.message == WM_LBUTTONDOWN) {
				key_pressed = true;
			}
		}
		Sleep(10); // 短暂休眠，减少CPU占用
	}
	
	// 清屏，准备开始游戏
	cleardevice();
}

// 更新摄像机位置函数
void update_camera() {
	// 计算理想的摄像机位置（使玩家位于屏幕中央）
	int target_x = soldier.x - WINDOW_WIDTH / 2 + BLOCK_SIZE / 2;
	int target_y = soldier.y - WINDOW_HEIGHT / 2 + BLOCK_SIZE / 2;
	
	// 限制摄像机不超出地图边界
	if (target_x < 0) target_x = 0;
	if (target_x > MAP_WIDTH - WINDOW_WIDTH) target_x = MAP_WIDTH - WINDOW_WIDTH;
	if (target_y < 0) target_y = 0;
	if (target_y > MAP_HEIGHT - WINDOW_HEIGHT) target_y = MAP_HEIGHT - WINDOW_HEIGHT;
	
	// 更新摄像机位置
	camera.x = target_x;
	camera.y = target_y;
}

// 世界坐标转屏幕坐标
int world_to_screen_x(int world_x) {
	return world_x - camera.x;
}

int world_to_screen_y(int world_y) {
	return world_y - camera.y;
}

// 屏幕坐标转世界坐标
int screen_to_world_x(int screen_x) {
	return screen_x + camera.x;
}

int screen_to_world_y(int screen_y) {
	return screen_y + camera.y;
}

// 绘制指向炸弹位置的箭头



// 初始化地图和迷雾
void init_map() {
    // 初始化所有格子为空
    for (int x = 0; x < MAP_GRID_WIDTH; x++) {
        for (int y = 0; y < MAP_GRID_HEIGHT; y++) {
            map_grid[x][y] = GRID_EMPTY;
            fog_of_war[x][y] = FOG_UNKNOWN;
        }
    }
    
    // 将障碍物信息添加到地图格子
    Obstacle* obs_current = obstacle_head;
    while (obs_current != NULL) {
        int grid_x = obs_current->x / MAP_GRID_SIZE;
        int grid_y = obs_current->y / MAP_GRID_SIZE;
        if (grid_x >= 0 && grid_x < MAP_GRID_WIDTH && grid_y >= 0 && grid_y < MAP_GRID_HEIGHT) {
            map_grid[grid_x][grid_y] = GRID_OBSTACLE;
        }
        obs_current = obs_current->next;
    }
    
    // 初始化玩家周围区域为可见
    update_visibility();
}

// 更新可见性
void update_visibility() {
    int player_grid_x = soldier.x / MAP_GRID_SIZE;
    int player_grid_y = soldier.y / MAP_GRID_SIZE;
    int visibility_range = 5; // 可见范围（格子数）
    
    // 将所有当前可见区域改为已探索
    for (int x = 0; x < MAP_GRID_WIDTH; x++) {
        for (int y = 0; y < MAP_GRID_HEIGHT; y++) {
            if (fog_of_war[x][y] == FOG_VISIBLE) {
                fog_of_war[x][y] = FOG_EXPLORED;
            }
        }
    }
    
    // 更新玩家周围区域为可见
    for (int x = player_grid_x - visibility_range; x <= player_grid_x + visibility_range; x++) {
        for (int y = player_grid_y - visibility_range; y <= player_grid_y + visibility_range; y++) {
            if (x >= 0 && x < MAP_GRID_WIDTH && y >= 0 && y < MAP_GRID_HEIGHT) {
                // 计算到玩家的距离
                int dx = x - player_grid_x;
                int dy = y - player_grid_y;
                double distance = sqrt(dx*dx + dy*dy);
                
                if (distance <= visibility_range) {
                    fog_of_war[x][y] = FOG_VISIBLE;
                }
            }
        }
    }
}

// 修改draw_game函数，绘制迷雾战争

// 添加小地图绘制函数
void draw_minimap() {
	int minimap_size = 150;
	int minimap_x = WINDOW_WIDTH - minimap_size - 10;
	int minimap_y = 10;
	int border = 2;
	
	// 绘制小地图边框
	setlinecolor(WHITE);
	setfillcolor(BLACK);
	fillrectangle(minimap_x - border, minimap_y - border, 
		minimap_x + minimap_size + border, minimap_y + minimap_size + border);
	
	// 计算小地图比例
	double scale_x = (double)minimap_size / MAP_WIDTH;
	double scale_y = (double)minimap_size / MAP_HEIGHT;
	
	// 绘制小地图内容
	for (int x = 0; x < MAP_GRID_WIDTH; x++) {
		for (int y = 0; y < MAP_GRID_HEIGHT; y++) {
			int mm_x = minimap_x + (int)(x * MAP_GRID_SIZE * scale_x);
			int mm_y = minimap_y + (int)(y * MAP_GRID_SIZE * scale_y);
			int mm_size = std::max(1, (int)(MAP_GRID_SIZE * scale_x));
			
			// 根据迷雾状态绘制
			if (fog_of_war[x][y] == FOG_UNKNOWN) {
				// 未探索区域不绘制
			} else if (fog_of_war[x][y] == FOG_EXPLORED) {
				// 已探索区域绘制为深灰色
				setfillcolor(RGB(50, 50, 50));
				fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				
				// 绘制已知的障碍物
				if (map_grid[x][y] == GRID_OBSTACLE) {
					setfillcolor(GRAY);
					fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				}
			} else if (fog_of_war[x][y] == FOG_VISIBLE) {
				// 可见区域绘制为浅灰色
				setfillcolor(RGB(200, 200, 200));
				fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				
				// 绘制可见区域的内容
				if (map_grid[x][y] == GRID_OBSTACLE) {
					setfillcolor(GRAY);
					fillrectangle(mm_x, mm_y, mm_x + mm_size, mm_y + mm_size);
				}
			}
		}
	}
	
	// 绘制玩家位置（蓝色点）
	int player_mm_x = minimap_x + (int)(soldier.x * scale_x);
	int player_mm_y = minimap_y + (int)(soldier.y * scale_y);
	setfillcolor(BLUE);
	fillrectangle(player_mm_x, player_mm_y, 
		player_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)), 
		player_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));
	
	// 绘制炸弹位置（绿色点），如果已放置
	if (bomb.placed) {
		int bomb_mm_x = minimap_x + (int)(bomb.x * scale_x);
		int bomb_mm_y = minimap_y + (int)(bomb.y * scale_y);
		setfillcolor(GREEN);
		fillrectangle(bomb_mm_x, bomb_mm_y, 
			bomb_mm_x + std::max(2, (int)(BLOCK_SIZE * scale_x)), 
			bomb_mm_y + std::max(2, (int)(BLOCK_SIZE * scale_y)));
	}
	
	// 绘制当前视野范围（白色矩形）
	int view_mm_x = minimap_x + (int)(camera.x * scale_x);
	int view_mm_y = minimap_y + (int)(camera.y * scale_y);
	int view_mm_width = (int)(WINDOW_WIDTH * scale_x);
	int view_mm_height = (int)(WINDOW_HEIGHT * scale_y);
	setlinecolor(WHITE);
	rectangle(view_mm_x, view_mm_y, 
		view_mm_x + view_mm_width, view_mm_y + view_mm_height);
}

// 在init_game函数中添加地图初始化


// 定义侦测范围
#define DETECTION_RANGE 200

// 新增函数：绘制侦测范围
void draw_detection_range(int x, int y) {
    // 绘制半透明圆形范围
    setlinecolor(RGB(255, 0, 0)); // 红色边框
    setfillcolor(RGB(255, 0, 0)); // 修复 RGB 宏参数错误
    fillellipse(x, y, DETECTION_RANGE, DETECTION_RANGE);
}
